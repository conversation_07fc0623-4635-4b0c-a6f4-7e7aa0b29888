-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Sep 20, 2025 at 07:51 AM
-- Server version: 11.8.3-MariaDB-log
-- PHP Version: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u939614337_shreeji_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `cart_items`
--

CREATE TABLE `cart_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `product_id` bigint(20) UNSIGNED NOT NULL,
  `quantity` int(11) NOT NULL,
  `size` varchar(255) DEFAULT NULL,
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`options`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cart_items`
--

INSERT INTO `cart_items` (`id`, `user_id`, `session_id`, `product_id`, `quantity`, `size`, `options`, `created_at`, `updated_at`) VALUES
(2, NULL, 'EKrB7TuOqLKTP8P1DWut4Ey4q12U3cWvkBZC5YuU', 24, 1, NULL, NULL, '2025-08-23 10:45:49', '2025-08-23 11:06:04'),
(3, NULL, 'EKrB7TuOqLKTP8P1DWut4Ey4q12U3cWvkBZC5YuU', 2, 1, NULL, NULL, '2025-08-23 11:18:20', '2025-08-23 11:18:20'),
(5, NULL, 'test_session_1755968521', 1, 2, NULL, NULL, '2025-08-23 11:32:01', '2025-08-23 11:32:01'),
(17, 3, NULL, 1, 1, NULL, NULL, '2025-08-24 08:23:35', '2025-08-24 08:23:35'),
(18, 3, NULL, 41, 1, NULL, NULL, '2025-08-24 08:23:35', '2025-08-24 08:23:35'),
(22, NULL, 'IecluvNtkVGeLpCWoyZdWAwrbCXG7qHHsIz2AhIj', 12, 1, NULL, NULL, '2025-09-04 01:52:20', '2025-09-04 01:52:20'),
(23, NULL, 'IecluvNtkVGeLpCWoyZdWAwrbCXG7qHHsIz2AhIj', 43, 1, NULL, NULL, '2025-09-04 01:52:28', '2025-09-04 01:52:28'),
(26, NULL, 'AztH2dH4iJ5OPTek80uVsdeo2gm3lYjIDDUGej1b', 2, 2, NULL, NULL, '2025-09-07 10:56:16', '2025-09-07 11:10:06'),
(27, 2, NULL, 2, 2, NULL, NULL, '2025-09-07 11:22:47', '2025-09-07 11:22:47'),
(29, NULL, 'XwNiqDihhx0ucVlzO5tXwExBkONMgPpPbFdY00Qn', 20, 1, NULL, NULL, '2025-09-08 03:58:00', '2025-09-08 03:58:00'),
(30, NULL, 'XwNiqDihhx0ucVlzO5tXwExBkONMgPpPbFdY00Qn', 21, 1, NULL, NULL, '2025-09-08 03:58:04', '2025-09-08 03:58:04'),
(33, NULL, 'ktvbsqDpmVRDfrnSiEDYRmSTNKVbeJTJpOuAp8wT', 58, 1, NULL, NULL, '2025-09-08 06:13:40', '2025-09-08 06:13:40'),
(34, NULL, 'ktvbsqDpmVRDfrnSiEDYRmSTNKVbeJTJpOuAp8wT', 2, 1, NULL, NULL, '2025-09-08 06:13:46', '2025-09-08 06:13:46'),
(35, NULL, 'ktvbsqDpmVRDfrnSiEDYRmSTNKVbeJTJpOuAp8wT', 13, 1, NULL, NULL, '2025-09-08 06:13:53', '2025-09-08 06:13:53'),
(36, NULL, 'ktvbsqDpmVRDfrnSiEDYRmSTNKVbeJTJpOuAp8wT', 7, 1, NULL, NULL, '2025-09-08 06:13:59', '2025-09-08 06:13:59'),
(37, NULL, 'ktvbsqDpmVRDfrnSiEDYRmSTNKVbeJTJpOuAp8wT', 44, 1, NULL, NULL, '2025-09-08 06:14:11', '2025-09-08 06:14:11'),
(41, NULL, 'lxKevlI70jegKqKVJAFspmeufoUOwSuhAJBW39xT', 2, 1, NULL, NULL, '2025-09-08 13:33:48', '2025-09-08 13:33:48'),
(42, NULL, 'lxKevlI70jegKqKVJAFspmeufoUOwSuhAJBW39xT', 13, 1, NULL, NULL, '2025-09-08 13:33:49', '2025-09-08 13:33:49'),
(45, NULL, '1vEdLWKIeX0ms0HiGo90kGJGscAFAeOkVpK2BFTB', 13, 1, NULL, NULL, '2025-09-08 13:40:03', '2025-09-08 13:40:03'),
(50, NULL, 'eUa3HrJ4ZPkKRASwV7S5QA8ml98ynVH3PmIxBgVx', 52, 6, NULL, NULL, '2025-09-10 18:10:40', '2025-09-10 18:12:48'),
(53, NULL, 'uONXmDSKG7LmEjA59QLps5U0RQejjoIgPlufvSkX', 13, 1, NULL, NULL, '2025-09-20 07:07:29', '2025-09-20 07:07:29');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `description`, `image`, `is_active`, `is_featured`, `sort_order`, `created_at`, `updated_at`) VALUES
(2, 'Auntie Tanish jewellery', 'auntie-tanish-jewellery', 'Auntie tarnishJewellery\r\nWhere timeless design meets tarnish-free shine.✨🫶🏻\r\nCrafted to glow with you — every day, every occasion.', 'categories/NRFwfhEigXDBiZzv4ABFklDbZEPRmKEvk0yjWVy8.jpg', 1, 1, 2, '2025-06-18 04:02:08', '2025-07-14 10:38:43'),
(3, 'Golden and silver replica earrings', 'golden-and-silver-replica-earrings', 'Elegance in Every Twist – Golden & Silver Replica Hoops for Everyday Glam.', 'categories/7w2AjP85JrwIgQLvw8Kk6i1pktjJ5ej0k9LP2Cog.jpg', 1, 1, 3, '2025-06-18 04:02:08', '2025-07-14 10:58:36'),
(5, 'Ad stone necklace', 'ad-stone-necklace', 'Complete jewelry sets for special occasions.', 'categories/1ydiCyhk9OkZouDYoF288NxwlGyCVFaX49CHlvKK.jpg', 1, 1, 5, '2025-06-18 04:02:08', '2025-07-14 10:47:59'),
(6, 'Accessories', 'accessories', 'Traditional jewelry accessories', 'categories/y7FqdTcSS7JmYz3LKtmhyfu4Qt20YwrNKvOTVzE8.jpg', 1, 1, 6, '2025-06-18 05:22:59', '2025-07-14 10:50:39'),
(7, 'Nose Rings', 'nose-rings', 'Traditional nose rings and nath', 'categories/8X5l2U5cnKz1wK2rZ4pnSEhoy1xIw42QpaRk13oJ.jpg', 1, 1, 7, '2025-06-18 05:22:59', '2025-07-14 10:51:44'),
(9, 'Oxidise earrings', 'oxidise-earrings', 'Bold. Beautiful. Boho vibes — elevate your ethnic look with oxidised charm.', 'categories/ewjwVE2oG4q1jbIqYa5DOnI5uoaWnyQqnwDkRYX2.jpg', 1, 1, 0, '2025-07-14 10:58:00', '2025-07-14 10:58:00');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(5, '2025_06_13_061908_create_categories_table', 1),
(6, '2025_06_13_061923_create_products_table', 1),
(7, '2025_06_13_061953_create_orders_table', 1),
(8, '2025_06_13_062019_create_order_items_table', 1),
(9, '2025_06_13_062026_create_cart_items_table', 1),
(10, '2025_06_13_062311_create_wishlists_table', 1),
(11, '2025_06_13_063443_add_fields_to_users_table', 1),
(12, '2025_06_13_093020_add_tracking_fields_to_orders_table', 1),
(13, '2025_06_13_101559_add_login_fields_to_users_table', 1),
(14, '2025_06_17_170431_add_razorpay_fields_to_orders_table', 1),
(15, '2025_06_18_160904_create_reviews_table', 1),
(16, '2025_07_01_165931_create_pages_table', 1),
(17, '2025_07_01_174902_add_admin_profile_fields_to_users_table', 1),
(18, '2025_07_13_173512_add_is_featured_to_categories_table', 1),
(19, '2025_07_18_165225_create_otp_verifications_table', 1),
(20, '2025_07_18_165259_modify_users_table_for_mobile_auth', 1),
(21, '2025_07_18_172218_fix_otp_verifications_purpose_column', 1),
(22, '2025_01_04_000000_create_promocodes_table', 2),
(23, '2025_01_04_000001_create_promocode_usages_table', 2),
(24, '2025_09_07_143159_create_settings_table', 3);

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `order_number` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `status` enum('pending','confirmed','processing','shipped','delivered','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `subtotal` decimal(10,2) NOT NULL,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `shipping_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'INR',
  `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(255) DEFAULT NULL,
  `payment_transaction_id` varchar(255) DEFAULT NULL,
  `payment_gateway_order_id` varchar(255) DEFAULT NULL,
  `payment_gateway` varchar(255) NOT NULL DEFAULT 'razorpay',
  `payment_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`payment_details`)),
  `razorpay_payment_id` varchar(255) DEFAULT NULL,
  `razorpay_signature` varchar(255) DEFAULT NULL,
  `payment_completed_at` timestamp NULL DEFAULT NULL,
  `billing_address` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`billing_address`)),
  `shipping_address` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`shipping_address`)),
  `shipping_method` varchar(255) DEFAULT NULL,
  `tracking_number` varchar(255) DEFAULT NULL,
  `courier_service` varchar(255) NOT NULL DEFAULT 'India Post',
  `tracking_url` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `status_history` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`status_history`)),
  `delivery_confirmation_method` varchar(255) DEFAULT NULL,
  `delivered_to` varchar(255) DEFAULT NULL,
  `delivery_notes` text DEFAULT NULL,
  `shipped_at` timestamp NULL DEFAULT NULL,
  `delivered_at` timestamp NULL DEFAULT NULL,
  `confirmed_at` timestamp NULL DEFAULT NULL,
  `processing_at` timestamp NULL DEFAULT NULL,
  `packed_at` timestamp NULL DEFAULT NULL,
  `out_for_delivery_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `order_number`, `user_id`, `status`, `subtotal`, `tax_amount`, `shipping_amount`, `discount_amount`, `total_amount`, `currency`, `payment_status`, `payment_method`, `payment_transaction_id`, `payment_gateway_order_id`, `payment_gateway`, `payment_details`, `razorpay_payment_id`, `razorpay_signature`, `payment_completed_at`, `billing_address`, `shipping_address`, `shipping_method`, `tracking_number`, `courier_service`, `tracking_url`, `notes`, `admin_notes`, `updated_by`, `status_history`, `delivery_confirmation_method`, `delivered_to`, `delivery_notes`, `shipped_at`, `delivered_at`, `confirmed_at`, `processing_at`, `packed_at`, `out_for_delivery_at`, `created_at`, `updated_at`) VALUES
(2, 'ORD-2025-001', 2, 'confirmed', 340.00, 61.20, 200.00, 0.00, 601.20, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, 'test', 'Akanksha', '[{\"status\":\"confirmed\",\"timestamp\":\"2025-08-26T04:44:20.326724Z\",\"updated_by\":\"Akanksha\",\"notes\":\"test\"}]', NULL, NULL, NULL, NULL, NULL, '2025-08-25 23:14:20', NULL, NULL, NULL, '2025-08-23 11:36:27', '2025-08-25 23:14:20'),
(3, 'ORD-2025-002', 2, 'pending', 340.00, 61.20, 200.00, 0.00, 601.20, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 11:40:45', '2025-08-23 11:40:45'),
(4, 'ORD-2025-003', 2, 'pending', 340.00, 61.20, 200.00, 0.00, 601.20, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 11:41:08', '2025-08-23 11:41:08'),
(5, 'ORD-2025-004', 2, 'pending', 340.00, 61.20, 200.00, 0.00, 601.20, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 11:41:47', '2025-08-23 11:41:47'),
(6, 'ORD-2025-005', 2, 'pending', 340.00, 61.20, 200.00, 0.00, 601.20, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 11:45:27', '2025-08-23 11:45:27'),
(7, 'ORD-2025-006', 2, 'confirmed', 340.00, 61.20, 200.00, 0.00, 601.20, 'INR', 'paid', 'razorpay', 'demo_payment_1755969419780', NULL, 'razorpay', NULL, 'demo_payment_1755969419780', 'demo_signature_1755969419780', '2025-08-23 11:47:00', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"delhi\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"delhi\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, 'HGDGHV', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 11:47:00', NULL, NULL, NULL, '2025-08-23 11:46:47', '2025-08-23 11:47:00'),
(8, 'ORD-2025-007', 2, 'confirmed', 1192.00, 214.56, 200.00, 0.00, 1606.56, 'INR', 'paid', 'razorpay', 'pay_R8rGMJGXuihjdz', 'order_R8rBRu5lsYTLDN', 'razorpay', '{\"id\":\"pay_R8rGMJGXuihjdz\",\"entity\":\"payment\",\"amount\":160656,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_R8rBRu5lsYTLDN\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-007\",\"card_id\":\"card_R8rGMp3bk0gG4J\",\"card\":{\"id\":\"card_R8rGMp3bk0gG4J\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"token_id\":\"token_R8rGNI1JM7cip9\",\"notes\":{\"order_id\":8,\"customer_name\":\"SANDEEP KUMAR GAVEL\",\"customer_email\":\"<EMAIL>\"},\"fee\":4820,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"637835\"},\"created_at\":**********}', 'pay_R8rGMJGXuihjdz', '787767a5aec4ab797b12ce7921a0d23017253207e113f4cdc581e3d34fc378ba', '2025-08-23 11:57:44', '{\"name\":\"SANDEEP KUMAR GAVEL\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"gond bordi,purena,kharsia,raigarh,cg, near matalha talab\",\"city\":\"Raigarh\",\"state\":\"gujarat\",\"pincode\":\"496661\",\"country\":\"india\"}', '{\"address\":\"gond bordi,purena,kharsia,raigarh,cg, near matalha talab\",\"city\":\"Raigarh\",\"state\":\"gujarat\",\"pincode\":\"496661\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 11:57:44', NULL, NULL, NULL, '2025-08-23 11:52:30', '2025-08-23 11:57:44'),
(9, 'ORD-2025-008', 2, 'confirmed', 698.00, 125.64, 200.00, 0.00, 1023.64, 'INR', 'paid', 'razorpay', 'pay_R8rdwDCw3tijYC', 'order_R8rdIerOsNqMyj', 'razorpay', '{\"id\":\"pay_R8rdwDCw3tijYC\",\"entity\":\"payment\",\"amount\":102364,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_R8rdIerOsNqMyj\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-008\",\"card_id\":\"card_R8rdwQxKnskD61\",\"card\":{\"id\":\"card_R8rdwQxKnskD61\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":9,\"customer_name\":\"Prashant Gavel\",\"customer_email\":\"<EMAIL>\"},\"fee\":3071,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"250659\"},\"created_at\":**********}', 'pay_R8rdwDCw3tijYC', '9eb211b9dcd0df61b3e3251c84ba9898fb79f82d3b8f2e43d76182161670c54e', '2025-08-23 12:19:57', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"gujarat\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"gujarat\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 12:19:57', NULL, NULL, NULL, '2025-08-23 12:18:52', '2025-08-23 12:19:57'),
(10, 'ORD-2025-009', 2, 'confirmed', 698.00, 125.64, 200.00, 0.00, 1023.64, 'INR', 'paid', 'razorpay', 'pay_R8rhBW8pTiFDf3', 'order_R8rgdheeuQ5fj7', 'razorpay', '{\"id\":\"pay_R8rhBW8pTiFDf3\",\"entity\":\"payment\",\"amount\":102364,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_R8rgdheeuQ5fj7\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-009\",\"card_id\":\"card_R8rhBk8HnewjQK\",\"card\":{\"id\":\"card_R8rhBk8HnewjQK\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":10,\"customer_name\":\"Prashant Gavel\",\"customer_email\":\"<EMAIL>\"},\"fee\":3071,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"383866\"},\"created_at\":**********}', 'pay_R8rhBW8pTiFDf3', 'b85f28aa2bb6f5d07a291874e59519e9d252e0b10e5d22caf5ac2dd62e0cd7b3', '2025-08-23 12:23:00', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"delhi\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"delhi\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 12:23:00', NULL, NULL, NULL, '2025-08-23 12:22:03', '2025-08-23 12:23:00'),
(11, 'ORD-2025-010', 2, 'confirmed', 295.00, 53.10, 200.00, 0.00, 548.10, 'INR', 'paid', 'razorpay', 'pay_R8rkIQ4LFMZUE2', 'order_R8rjtmNS7DVtPy', 'razorpay', '{\"id\":\"pay_R8rkIQ4LFMZUE2\",\"entity\":\"payment\",\"amount\":54810,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_R8rjtmNS7DVtPy\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-010\",\"card_id\":\"card_R8rkIdAZVMYTNJ\",\"card\":{\"id\":\"card_R8rkIdAZVMYTNJ\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":11,\"customer_name\":\"Prashant Gavel\",\"customer_email\":\"<EMAIL>\"},\"fee\":1645,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"637374\"},\"created_at\":**********}', 'pay_R8rkIQ4LFMZUE2', '19bd1312949e563acfbee356415599d8fcfbbe453a380be0772af0717759717a', '2025-08-23 12:26:07', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"rajasthan\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"rajasthan\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 12:26:07', NULL, NULL, NULL, '2025-08-23 12:25:08', '2025-08-23 12:26:07'),
(12, 'ORD-2025-011', 2, 'pending', 852.00, 153.36, 200.00, 0.00, 1205.36, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"tamil-nadu\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"tamil-nadu\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 12:50:25', '2025-08-23 12:50:25'),
(13, 'ORD-2025-012', 2, 'pending', 852.00, 153.36, 200.00, 0.00, 1205.36, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"tamil-nadu\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"tamil-nadu\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 12:50:43', '2025-08-23 12:50:43'),
(14, 'ORD-2025-013', 2, 'confirmed', 1107.00, 199.26, 200.00, 0.00, 1506.26, 'INR', 'paid', 'razorpay', 'pay_R8sBoz8Zr57GLn', 'order_R8sBPuJKD7awUO', 'razorpay', '{\"id\":\"pay_R8sBoz8Zr57GLn\",\"entity\":\"payment\",\"amount\":150626,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_R8sBPuJKD7awUO\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-013\",\"card_id\":\"card_R8sBpCwv23cri2\",\"card\":{\"id\":\"card_R8sBpCwv23cri2\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":14,\"customer_name\":\"Prashant Gavel\",\"customer_email\":\"<EMAIL>\"},\"fee\":4519,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"599143\"},\"created_at\":**********}', 'pay_R8sBoz8Zr57GLn', '4e8ec3e6cc995dec2379a2e2d44808cce373ed9fae57e8772d6e61c03109a77a', '2025-08-23 12:51:59', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"rajasthan\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"rajasthan\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-23 12:51:59', NULL, NULL, NULL, '2025-08-23 12:51:10', '2025-08-23 12:51:59'),
(15, 'ORD-2025-014', 2, 'confirmed', 698.00, 125.64, 200.00, 0.00, 1023.64, 'INR', 'paid', 'razorpay', 'pay_R9BrxlhInQKXky', 'order_R9Br1DJEAnl77g', 'razorpay', '{\"id\":\"pay_R9BrxlhInQKXky\",\"entity\":\"payment\",\"amount\":102364,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_R9Br1DJEAnl77g\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-014\",\"card_id\":\"card_R9BrxzCU2L4Ohy\",\"card\":{\"id\":\"card_R9BrxzCU2L4Ohy\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":15,\"customer_name\":\"Prashant Gavel\",\"customer_email\":\"<EMAIL>\"},\"fee\":3071,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"176740\"},\"created_at\":**********}', 'pay_R9BrxlhInQKXky', '7891d1b1f20d1463ab5c6ebb88fdcfb4a7600b63d2d4fa0ced0d3dea68641d2c', '2025-08-24 08:07:02', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"karnataka\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-24 08:07:02', NULL, NULL, NULL, '2025-08-24 08:05:39', '2025-08-24 08:07:02'),
(16, 'ORD-2025-015', 3, 'pending', 852.00, 153.36, 200.00, 0.00, 1205.36, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Akanksha Tiwari\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"MP\",\"city\":\"Sahdol\",\"state\":\"gujarat\",\"pincode\":\"496661\",\"country\":\"india\"}', '{\"address\":\"MP\",\"city\":\"Sahdol\",\"state\":\"gujarat\",\"pincode\":\"496661\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-24 08:13:54', '2025-08-24 08:13:54'),
(17, 'ORD-2025-016', 3, 'pending', 852.00, 153.36, 200.00, 0.00, 1205.36, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Akanksha Tiwari\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"MP\",\"city\":\"Sahdol\",\"state\":\"gujarat\",\"pincode\":\"496661\",\"country\":\"india\"}', '{\"address\":\"MP\",\"city\":\"Sahdol\",\"state\":\"gujarat\",\"pincode\":\"496661\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-24 08:14:20', '2025-08-24 08:14:20'),
(26, 'ORD-2025-017', 3, 'cancelled', 1580.00, 284.40, 200.00, 0.00, 2064.40, 'INR', 'failed', 'razorpay', 'pay_R9C8ALpkrnkFWP', 'order_R9C7gn0IRPIOB3', 'razorpay', NULL, 'pay_R9C8ALpkrnkFWP', '146ee22974dbd4c9832cbd59bdd4bfab200810e54926c12b56c7884d095067c2', '2025-08-24 08:22:35', '{\"name\":\"Akanksha\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"rajasthan\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"rajasthan\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-24 08:22:35', NULL, NULL, NULL, '2025-08-24 08:21:29', '2025-08-24 08:23:35'),
(27, 'ORD-2025-018', 3, 'confirmed', 640.00, 115.20, 200.00, 0.00, 955.20, 'INR', 'paid', 'razorpay', 'pay_R9CBveYusYYl2y', 'order_R9CBXorWzD75E4', 'razorpay', '{\"id\":\"pay_R9CBveYusYYl2y\",\"entity\":\"payment\",\"amount\":95520,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_R9CBXorWzD75E4\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-018\",\"card_id\":\"card_R9CBvvv4YYopca\",\"card\":{\"id\":\"card_R9CBvvv4YYopca\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":27,\"customer_name\":\"Akanksha\",\"customer_email\":\"<EMAIL>\"},\"fee\":2866,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"520192\"},\"created_at\":**********}', 'pay_R9CBveYusYYl2y', '3487f9a866ea5fb1c6a6f30f66e43027400986ae37bc8763cedafb809910f74b', '2025-08-24 08:25:58', '{\"name\":\"Akanksha\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"tamil-nadu\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"tamil-nadu\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-24 08:25:58', NULL, NULL, NULL, '2025-08-24 08:25:08', '2025-08-24 08:25:58'),
(28, 'ORD-2025-019', 2, 'confirmed', 225.00, 40.50, 200.00, 0.00, 465.50, 'INR', 'paid', 'razorpay', 'pay_RDQnrsyx7542de', 'order_RDQmt4C4vjN7h9', 'razorpay', '{\"id\":\"pay_RDQnrsyx7542de\",\"entity\":\"payment\",\"amount\":46550,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_RDQmt4C4vjN7h9\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-019\",\"card_id\":\"card_RDQns8q2u6313R\",\"card\":{\"id\":\"card_RDQns8q2u6313R\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":28,\"customer_name\":\"Prashant Gavel\",\"customer_email\":\"<EMAIL>\"},\"fee\":1397,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"476541\"},\"created_at\":**********}', 'pay_RDQnrsyx7542de', 'bd2b0040ef050cdebcf6c810809aa3a58d89429217a26b451170ad3a930b9e0f', '2025-09-04 01:19:33', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"gujarat\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"gujarat\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-04 01:19:33', NULL, NULL, NULL, '2025-09-04 01:18:03', '2025-09-04 01:19:33'),
(29, 'ORD-2025-020', 2, 'pending', 1396.00, -24.24, 500.00, 2204.00, -332.24, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"SANDEEP KUMAR GAVEL\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"gond bordi,purena,kharsia,raigarh,cg, near matalha talab\",\"city\":\"Raigarh\",\"state\":\"chhattisgarh\",\"pincode\":\"496661\",\"country\":\"india\"}', '{\"address\":\"gond bordi,purena,kharsia,raigarh,cg, near matalha talab\",\"city\":\"Raigarh\",\"state\":\"chhattisgarh\",\"pincode\":\"496661\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-07 11:10:35', '2025-09-07 11:10:35'),
(30, 'ORD-2025-021', 2, 'cancelled', 1396.00, 41.88, 500.00, 0.00, 1937.88, 'INR', 'failed', 'razorpay', NULL, 'order_REmgZT7WnK9Wd0', 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-07 11:22:31', '2025-09-07 11:22:47'),
(31, 'ORD-2025-022', 2, 'pending', 698.00, 20.94, 500.00, 0.00, 1218.94, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-07 17:21:25', '2025-09-07 17:21:25'),
(32, 'ORD-2025-023', 2, 'pending', 698.00, 20.94, 500.00, 0.00, 1218.94, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-07 17:21:38', '2025-09-07 17:21:38'),
(33, 'ORD-2025-024', 2, 'pending', 698.00, 20.94, 500.00, 0.00, 1218.94, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-07 17:21:46', '2025-09-07 17:21:46'),
(34, 'ORD-2025-025', 2, 'pending', 698.00, 20.94, 500.00, 0.00, 1218.94, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-07 17:21:53', '2025-09-07 17:21:53'),
(35, 'ORD-2025-026', 2, 'pending', 698.00, 20.94, 500.00, 0.00, 1218.94, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-07 17:22:32', '2025-09-07 17:22:32'),
(36, 'ORD-2025-027', 2, 'confirmed', 698.00, 20.94, 500.00, 0.00, 1218.94, 'INR', 'paid', 'razorpay', 'pay_REnF7rAlbaEVGG', 'order_REnEENbK8xB7TP', 'razorpay', '{\"id\":\"pay_REnF7rAlbaEVGG\",\"entity\":\"payment\",\"amount\":121894,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_REnEENbK8xB7TP\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-027\",\"card_id\":\"card_REnF83kHOI1pyG\",\"card\":{\"id\":\"card_REnF83kHOI1pyG\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":36,\"customer_name\":\"Prashant Gavel\",\"customer_email\":\"<EMAIL>\"},\"fee\":3657,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"358761\"},\"created_at\":**********}', 'pay_REnF7rAlbaEVGG', '4f66e6c4a04e565c7d0e5da92c24da4028494c55270f4d1d7642a0104a92fec2', '2025-09-07 17:25:37', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-07 17:25:37', NULL, NULL, NULL, '2025-09-07 17:24:24', '2025-09-07 17:25:37'),
(37, 'ORD-2025-028', 3, 'confirmed', 659.00, 19.77, 500.00, 0.00, 1178.77, 'INR', 'paid', 'razorpay', 'pay_RF7NJr9HyC4dUL', 'order_RF7MtEOEqy74un', 'razorpay', '{\"id\":\"pay_RF7NJr9HyC4dUL\",\"entity\":\"payment\",\"amount\":117877,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_RF7MtEOEqy74un\",\"invoice_id\":null,\"international\":false,\"method\":\"upi\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-028\",\"card_id\":null,\"bank\":null,\"wallet\":null,\"vpa\":\"success@razorpay\",\"email\":\"<EMAIL>\",\"contact\":\"+************\",\"notes\":{\"order_id\":37,\"customer_name\":\"Akanksha\",\"customer_email\":\"<EMAIL>\"},\"fee\":2782,\"tax\":424,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"rrn\":\"************\",\"upi_transaction_id\":\"733FEBDA139594E694CE7DCD104B8227\"},\"created_at\":**********,\"upi\":{\"vpa\":\"success@razorpay\"}}', 'pay_RF7NJr9HyC4dUL', '9a38eee894a93400991d2ba2fc8ab8143dff344a92c8a0dfe1640eb70fbec357', '2025-09-08 13:09:41', '{\"name\":\"Akanksha\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Shahdol\",\"city\":\"Shahdol\",\"state\":\"madhya-pradesh\",\"pincode\":\"484001\",\"country\":\"india\"}', '{\"address\":\"Shahdol\",\"city\":\"Shahdol\",\"state\":\"madhya-pradesh\",\"pincode\":\"484001\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-08 13:09:41', NULL, NULL, NULL, '2025-09-08 13:06:28', '2025-09-08 13:09:41'),
(38, 'ORD-2025-029', 2, 'pending', 1283.00, 34.64, 500.00, 128.30, 1689.34, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-08 13:34:18', '2025-09-08 13:34:18'),
(39, 'ORD-2025-030', 2, 'pending', 1283.00, 34.64, 500.00, 128.30, 1689.34, 'INR', 'pending', 'razorpay', NULL, NULL, 'razorpay', NULL, NULL, NULL, NULL, '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-08 13:34:29', '2025-09-08 13:34:29'),
(40, 'ORD-2025-031', 2, 'confirmed', 1284.00, 38.52, 500.00, 0.00, 1822.52, 'INR', 'paid', 'razorpay', 'pay_RF7rpx5oG7kThY', 'order_RF7r3EQglL7j5g', 'razorpay', '{\"id\":\"pay_RF7rpx5oG7kThY\",\"entity\":\"payment\",\"amount\":182252,\"currency\":\"INR\",\"status\":\"captured\",\"order_id\":\"order_RF7r3EQglL7j5g\",\"invoice_id\":null,\"international\":false,\"method\":\"card\",\"amount_refunded\":0,\"refund_status\":null,\"captured\":true,\"description\":\"Secure Payment for Order #ORD-2025-031\",\"card_id\":\"card_RF7rq9yxcoLGf7\",\"card\":{\"id\":\"card_RF7rq9yxcoLGf7\",\"entity\":\"card\",\"name\":\"\",\"last4\":\"8228\",\"network\":\"MasterCard\",\"type\":\"credit\",\"issuer\":\"HDFC\",\"international\":false,\"emi\":true,\"sub_type\":\"business\",\"token_iin\":null},\"bank\":null,\"wallet\":null,\"vpa\":null,\"email\":\"<EMAIL>\",\"contact\":\"+91**********\",\"notes\":{\"order_id\":40,\"customer_name\":\"Prashant Gavel\",\"customer_email\":\"<EMAIL>\"},\"fee\":5468,\"tax\":0,\"error_code\":null,\"error_description\":null,\"error_source\":null,\"error_step\":null,\"error_reason\":null,\"acquirer_data\":{\"auth_code\":\"123725\"},\"created_at\":**********}', 'pay_RF7rpx5oG7kThY', '6f21fad878d66f807c2d44cb23da6cb51771018dcb495be8ea8797a7355e362b', '2025-09-08 13:36:09', '{\"name\":\"Prashant Gavel\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', '{\"address\":\"Amanaka, Raipur\",\"city\":\"Raipur\",\"state\":\"chhattisgarh\",\"pincode\":\"492010\",\"country\":\"india\"}', 'standard', NULL, 'India Post', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-09-08 13:36:09', NULL, NULL, NULL, '2025-09-08 13:35:02', '2025-09-08 13:36:09');

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `order_id` bigint(20) UNSIGNED NOT NULL,
  `product_id` bigint(20) UNSIGNED NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `product_sku` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `size` varchar(255) DEFAULT NULL,
  `product_options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`product_options`)),
  `total` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `product_name`, `product_sku`, `price`, `quantity`, `size`, `product_options`, `total`, `created_at`, `updated_at`) VALUES
(1, 2, 12, 'Antique Peacock Motif Earrings with Pearl Drops', 'PEACOCK-GLD-6559', 340.00, 1, NULL, NULL, 340.00, '2025-08-23 11:36:27', '2025-08-23 11:36:27'),
(2, 3, 12, 'Antique Peacock Motif Earrings with Pearl Drops', 'PEACOCK-GLD-6559', 340.00, 1, NULL, NULL, 340.00, '2025-08-23 11:40:45', '2025-08-23 11:40:45'),
(3, 4, 12, 'Antique Peacock Motif Earrings with Pearl Drops', 'PEACOCK-GLD-6559', 340.00, 1, NULL, NULL, 340.00, '2025-08-23 11:41:08', '2025-08-23 11:41:08'),
(4, 5, 12, 'Antique Peacock Motif Earrings with Pearl Drops', 'PEACOCK-GLD-6559', 340.00, 1, NULL, NULL, 340.00, '2025-08-23 11:41:47', '2025-08-23 11:41:47'),
(5, 6, 12, 'Antique Peacock Motif Earrings with Pearl Drops', 'PEACOCK-GLD-6559', 340.00, 1, NULL, NULL, 340.00, '2025-08-23 11:45:27', '2025-08-23 11:45:27'),
(6, 7, 12, 'Antique Peacock Motif Earrings with Pearl Drops', 'PEACOCK-GLD-6559', 340.00, 1, NULL, NULL, 340.00, '2025-08-23 11:46:47', '2025-08-23 11:46:47'),
(7, 8, 12, 'Antique Peacock Motif Earrings with Pearl Drops', 'PEACOCK-GLD-6559', 340.00, 1, NULL, NULL, 340.00, '2025-08-23 11:52:30', '2025-08-23 11:52:30'),
(8, 8, 18, 'Antique Oxidized Dome Jhumka with Pearl Layers', 'OXDJ-PJ-2461', 852.00, 1, NULL, NULL, 852.00, '2025-08-23 11:52:30', '2025-08-23 11:52:30'),
(9, 9, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-08-23 12:18:52', '2025-08-23 12:18:52'),
(10, 10, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-08-23 12:22:03', '2025-08-23 12:22:03'),
(11, 11, 53, 'Black Stone Leaf Earrings', 'BSLE001', 295.00, 1, NULL, NULL, 295.00, '2025-08-23 12:25:08', '2025-08-23 12:25:08'),
(12, 12, 18, 'Antique Oxidized Dome Jhumka with Pearl Layers', 'OXDJ-PJ-2461', 852.00, 1, NULL, NULL, 852.00, '2025-08-23 12:50:25', '2025-08-23 12:50:25'),
(13, 13, 18, 'Antique Oxidized Dome Jhumka with Pearl Layers', 'OXDJ-PJ-2461', 852.00, 1, NULL, NULL, 852.00, '2025-08-23 12:50:43', '2025-08-23 12:50:43'),
(14, 14, 18, 'Antique Oxidized Dome Jhumka with Pearl Layers', 'OXDJ-PJ-2461', 852.00, 1, NULL, NULL, 852.00, '2025-08-23 12:51:10', '2025-08-23 12:51:10'),
(15, 14, 20, 'Oxidized Silver Floral Jhumka Earrings with Pearls', 'OX-JHUMKA-2034', 255.00, 1, NULL, NULL, 255.00, '2025-08-23 12:51:10', '2025-08-23 12:51:10'),
(16, 15, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-08-24 08:05:39', '2025-08-24 08:05:39'),
(17, 16, 18, 'Antique Oxidized Dome Jhumka with Pearl Layers', 'OXDJ-PJ-2461', 852.00, 1, NULL, NULL, 852.00, '2025-08-24 08:13:54', '2025-08-24 08:13:54'),
(18, 17, 18, 'Antique Oxidized Dome Jhumka with Pearl Layers', 'OXDJ-PJ-2461', 852.00, 1, NULL, NULL, 852.00, '2025-08-24 08:14:20', '2025-08-24 08:14:20'),
(25, 26, 1, 'Black & Gold Traditional Dangler', 'ER001', 876.00, 1, NULL, NULL, 876.00, '2025-08-24 08:21:29', '2025-08-24 08:21:29'),
(26, 26, 41, 'Pink and Silver Kundan Jhumka Earrings', 'KJ001', 704.00, 1, NULL, NULL, 704.00, '2025-08-24 08:21:29', '2025-08-24 08:21:29'),
(27, 27, 16, 'Peacock Blue Drop Earrings with Pearl Tassels', 'PCKBL-PRL-7921', 640.00, 1, NULL, NULL, 640.00, '2025-08-24 08:25:08', '2025-08-24 08:25:08'),
(28, 28, 58, '\"Ruby Peacock Earrings.\"', 'JE001', 225.00, 1, NULL, NULL, 225.00, '2025-09-04 01:18:03', '2025-09-04 01:18:03'),
(29, 29, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 2, NULL, NULL, 1396.00, '2025-09-07 11:10:35', '2025-09-07 11:10:35'),
(30, 30, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 2, NULL, NULL, 1396.00, '2025-09-07 11:22:31', '2025-09-07 11:22:31'),
(31, 31, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-09-07 17:21:25', '2025-09-07 17:21:25'),
(32, 32, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-09-07 17:21:38', '2025-09-07 17:21:38'),
(33, 33, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-09-07 17:21:46', '2025-09-07 17:21:46'),
(34, 34, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-09-07 17:21:53', '2025-09-07 17:21:53'),
(35, 35, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-09-07 17:22:32', '2025-09-07 17:22:32'),
(36, 36, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-09-07 17:24:24', '2025-09-07 17:24:24'),
(37, 37, 20, 'Oxidized Silver Floral Jhumka Earrings with Pearls', 'OX-JHUMKA-2034', 255.00, 2, NULL, NULL, 510.00, '2025-09-08 13:06:28', '2025-09-08 13:06:28'),
(38, 37, 23, 'Twist Hoop Gold-Tone Earrings', 'TGE-HOOP-002', 149.00, 1, NULL, NULL, 149.00, '2025-09-08 13:06:28', '2025-09-08 13:06:28'),
(39, 38, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-09-08 13:34:18', '2025-09-08 13:34:18'),
(40, 38, 13, 'Antique Floral Gold Necklace Set with Earrings', 'FLR-SET-9580', 585.00, 1, NULL, NULL, 585.00, '2025-09-08 13:34:18', '2025-09-08 13:34:18'),
(41, 39, 2, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'SKU-089235', 698.00, 1, NULL, NULL, 698.00, '2025-09-08 13:34:29', '2025-09-08 13:34:29'),
(42, 39, 13, 'Antique Floral Gold Necklace Set with Earrings', 'FLR-SET-9580', 585.00, 1, NULL, NULL, 585.00, '2025-09-08 13:34:29', '2025-09-08 13:34:29'),
(43, 40, 7, 'Antique Leaf Motif Necklace & Pearl Drop Earrings Set', 'SKU-271655', 699.00, 1, NULL, NULL, 699.00, '2025-09-08 13:35:02', '2025-09-08 13:35:02'),
(44, 40, 13, 'Antique Floral Gold Necklace Set with Earrings', 'FLR-SET-9580', 585.00, 1, NULL, NULL, 585.00, '2025-09-08 13:35:02', '2025-09-08 13:35:02');

-- --------------------------------------------------------

--
-- Table structure for table `otp_verifications`
--

CREATE TABLE `otp_verifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `phone` varchar(15) NOT NULL,
  `otp_code` varchar(6) NOT NULL,
  `purpose` enum('login','registration','checkout','test') NOT NULL DEFAULT 'login',
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_verified` tinyint(1) NOT NULL DEFAULT 0,
  `verified_at` timestamp NULL DEFAULT NULL,
  `attempts` int(11) NOT NULL DEFAULT 0,
  `resend_count` int(11) NOT NULL DEFAULT 0,
  `last_resent_at` timestamp NULL DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pages`
--

CREATE TABLE `pages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `excerpt` text DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meta_keywords`)),
  `template` varchar(255) NOT NULL DEFAULT 'default',
  `is_published` tinyint(1) NOT NULL DEFAULT 1,
  `show_in_menu` tinyint(1) NOT NULL DEFAULT 0,
  `menu_order` int(11) NOT NULL DEFAULT 0,
  `featured_image` varchar(255) DEFAULT NULL,
  `seo_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`seo_data`)),
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pages`
--

INSERT INTO `pages` (`id`, `title`, `slug`, `content`, `excerpt`, `meta_title`, `meta_description`, `meta_keywords`, `template`, `is_published`, `show_in_menu`, `menu_order`, `featured_image`, `seo_data`, `created_by`, `updated_by`, `published_at`, `created_at`, `updated_at`) VALUES
(1, 'About Us', 'about', '<h2>Our Story</h2><p>At Shreeji Marg Jewels, we create handmade fashion jewellery that reflects elegance, emotion, and individuality.</p><p>Each piece is crafted with care by skilled artisans, using premium, skin-friendly materials — no gold or silver, just pure style and craftsmanship.</p><h3>Our Heritage</h3><p>Our brand is built on a legacy of trust, consistency, and refined aesthetics. With years of industry insight, Shreeji Marg has grown into a name that stands for reliability, authenticity, and a commitment to evolving with the modern consumer.</p><h3>Our Craftsmanship</h3><p>Every piece of jewelry at ShreeJi is meticulously crafted by skilled artisans who have inherited their craft through generations. We combine traditional techniques with modern technology to create jewelry that is both timeless and contemporary.</p><h3>Our Values</h3><ul><li><strong>Quality:</strong> We use only the finest materials and maintain the highest standards of craftsmanship.</li><li><strong>Trust:</strong> We believe in transparent business practices and honest relationships with our customers.</li><li><strong>Innovation:</strong> We continuously evolve our designs to meet changing fashion trends while preserving traditional aesthetics.</li><li><strong>Service:</strong> We are committed to providing exceptional customer service at every step of your jewelry journey.</li></ul><h3>Our Promise</h3><p>At ShreeJi, we promise to deliver jewelry that not only enhances your beauty but also becomes a cherished part of your life\'s most important moments. Every piece is created with love, care, and attention to detail that you deserve.</p>', 'Discover the story behind ShreeJi Jewelry. Learn about our heritage, craftsmanship, and commitment to creating exquisite jewelry pieces.', 'About Us - ShreeJi Jewelry', 'Discover the story behind ShreeJi Jewelry. Learn about our heritage, craftsmanship, and commitment to creating exquisite jewelry pieces.', '[\"about\",\"jewelry\",\"heritage\",\"craftsmanship\",\"shreejimarg\"]', 'about', 1, 1, 1, NULL, NULL, NULL, 1, '2025-07-01 19:38:00', '2025-07-01 19:38:16', '2025-07-12 03:21:06'),
(2, 'Contact Us', 'contact', '<h2>Get in Touch</h2><p>We\'d love to hear from you. Get in touch with our team for any questions about our jewelry collection, custom designs, or services.</p><h3>Visit Our&nbsp;</h3><p>Experience the elegance of Shreeji Marg Jewellers in person.<br>Explore our exclusive collections, receive personalized service, and discover jewellery designed to celebrate every moment.</p><p><strong>Address:</strong></p><p>📍 <strong>Store Location:</strong><br>Shreeji Marg Jewellers<br>[Shahdol Madhya Pradesh]<br>[shahdol, M.P.,484001]<br>&nbsp;</p><h3>Contact Information</h3><p><strong>Phone:</strong> <a href=\"tel:+************\">+91-96174-59017</a><br><strong>Email:</strong> <a href=\"mailto:<EMAIL>\"><EMAIL></a><br><strong>WhatsApp:</strong> <a href=\"https://wa.me/************\">+91-96174-59017</a><br><strong>Instagram:</strong> <a href=\"https://instagram.com/shreejijewelry\">@shreejijewelry</a></p><h3>Store Hours</h3><p>Mon-Sat: 10:00 AM - 8:00 PM, Sun: 11:00 AM - 6:00 PM</p><h3>Customer Service</h3><p>Our customer service team is available to assist you with:</p><ul><li>Product inquiries and recommendations</li><li>Custom jewelry design consultations</li><li>Order status and tracking</li><li>Returns and exchanges</li><li>Jewelry care and maintenance</li><li>Warranty and repair services</li></ul><h3>Follow Us</h3><p>Stay connected with us on social media for the latest updates, new arrivals, and exclusive offers:</p><ul><li><strong>Instagram:</strong> <a href=\"https://instagram.com/shreejijewelry\">@</a>jewellery.shreejimarg</li><li><strong>Facebook:</strong> <a href=\"https://facebook.com/shreejijewelry\">@shreejijewelry</a></li><li><strong>YouTube:</strong> <a href=\"https://youtube.com/@shreejijewelry\">@shreejijewelry</a></li></ul>', 'Get in touch with ShreeJi Jewelry. Visit our store, call us, or send us a message for any inquiries about our jewelry collection.', 'Contact Us - ShreeJi Jewelry', 'Get in touch with ShreeJi Jewelry. Visit our store, call us, or send us a message for any inquiries about our jewelry collection.', '[\"contact\",\"jewelry store\",\"customer service\",\"shreejimarg\"]', 'contact', 1, 1, 2, NULL, NULL, NULL, 1, '2025-07-01 19:38:00', '2025-07-01 19:38:16', '2025-07-12 03:25:57'),
(3, 'Shipping Information', 'shipping', '<h2>Shipping Information</h2><p>Fast, secure, and reliable delivery for your precious jewelry.</p><h3>Domestic Shipping (India)</h3><ul><li><strong>Free Shipping:</strong> On orders above ₹5,00</li><li><strong>Standard Shipping:</strong> ₹150 for orders ₹2,000 - ₹4,999</li><li><strong>Express Shipping:</strong> ₹250 for orders below ₹2,000</li><li><strong>Delivery Time:</strong> 2-5 business days for major cities, 3-7 days for remote areas</li></ul><h3>International Shipping</h3><p>We ship to selected countries worldwide:</p><ul><li>USA &amp; Canada: 7-14 business days</li><li>UK &amp; Europe: 5-10 business days</li><li>Australia: 7-12 business days</li><li>UAE &amp; Middle East: 3-7 business days</li></ul><h3>Shipping Process</h3><ol><li><strong>Order Processing:</strong> 1-2 business days for verification and preparation</li><li><strong>Quality Check:</strong> Each piece is carefully inspected before packaging</li><li><strong>Secure Packaging:</strong> Premium packaging with bubble wrap and jewelry boxes</li><li><strong>Dispatch &amp; Tracking:</strong> Tracking details sent via email and SMS</li></ol><h3>Order Tracking</h3><p>Track your order easily through:</p><ul><li>Your account dashboard</li><li>Tracking link sent to your email</li><li>Customer service hotline</li><li>WhatsApp support</li></ul>', 'Learn about our shipping policies, delivery times, and shipping costs for jewelry orders at ShreeJi.', 'Shipping Information - ShreeJi Jewelry', 'Learn about our shipping policies, delivery times, and shipping costs for jewelry orders at ShreeJi.', '[\"shipping\",\"delivery\",\"jewelry shipping\",\"policies\"]', 'guide', 1, 0, 4, NULL, NULL, NULL, 3, '2025-07-01 19:38:00', '2025-07-01 19:38:16', '2025-08-25 22:29:17'),
(4, 'Returns & Exchange Policy', 'returns', '<h2>Returns & Exchange Policy</h2>\n<p>Hassle-free returns and exchanges for your peace of mind.</p>\n\n<h3>Return Eligibility</h3>\n<h4>✓ Eligible for Return:</h4>\n<ul>\n<li>Items in original condition with tags</li>\n<li>Unworn jewelry with original packaging</li>\n<li>Items returned within 30 days of delivery</li>\n<li>Items with original certificates and warranty cards</li>\n</ul>\n\n<h4>✗ Not Eligible for Return:</h4>\n<ul>\n<li>Customized or personalized jewelry</li>\n<li>Pierced earrings (for hygiene reasons)</li>\n<li>Items damaged due to misuse</li>\n<li>Items without original packaging</li>\n<li>Sale items (unless defective)</li>\n</ul>\n\n<h3>Return Process</h3>\n<ol>\n<li><strong>Initiate Return:</strong> Login to your account and request return from \"My Orders\"</li>\n<li><strong>Pack Securely:</strong> Pack item in original packaging with all accessories</li>\n<li><strong>Schedule Pickup:</strong> We\'ll arrange free pickup from your address</li>\n<li><strong>Get Refund:</strong> Refund processed within 5-7 business days</li>\n</ol>\n\n<h3>Exchange Policy</h3>\n<p><strong>Size Exchange:</strong> Free size exchange for rings and bangles within 30 days.</p>\n<p><strong>Design Exchange:</strong> Exchange for same or higher value item with price difference payment.</p>\n\n<h3>Refund Timeline</h3>\n<ul>\n<li>Credit/Debit Card: 5-7 business days</li>\n<li>Net Banking: 5-7 business days</li>\n<li>UPI/Wallet: 3-5 business days</li>\n<li>Cash on Delivery: 7-10 business days (₹50 processing fee)</li>\n</ul>', 'Learn about our hassle-free returns and exchange policy for jewelry purchases at ShreeJi.', 'Returns & Exchange Policy - ShreeJi Jewelry', 'Learn about our hassle-free returns and exchange policy for jewelry purchases at ShreeJi.', '[\"returns\",\"exchange\",\"policy\",\"jewelry returns\"]', 'policy', 1, 0, 11, NULL, NULL, NULL, NULL, '2025-07-01 19:38:16', '2025-07-01 19:38:16', '2025-07-01 19:38:16'),
(5, 'Size Guide', 'size-guide', '<h2>Size Guide</h2>\n<p>Find your perfect fit with our comprehensive sizing guide.</p>\n\n<h3>Ring Size Guide</h3>\n<h4>How to Measure:</h4>\n<ol>\n<li>Wrap a string around your finger where you\'d wear the ring</li>\n<li>Mark where the string overlaps</li>\n<li>Measure the string length with a ruler</li>\n<li>Use the chart below to find your ring size</li>\n</ol>\n\n<h4>Ring Size Chart</h4>\n<table>\n<tr><th>Indian Size</th><th>US Size</th><th>UK Size</th><th>Circumference (mm)</th></tr>\n<tr><td>8</td><td>4</td><td>H</td><td>46.8</td></tr>\n<tr><td>10</td><td>5</td><td>J</td><td>49.3</td></tr>\n<tr><td>12</td><td>6</td><td>L</td><td>51.8</td></tr>\n<tr><td>14</td><td>7</td><td>N</td><td>54.4</td></tr>\n<tr><td>16</td><td>8</td><td>P</td><td>56.9</td></tr>\n<tr><td>18</td><td>9</td><td>R</td><td>59.5</td></tr>\n<tr><td>20</td><td>10</td><td>T</td><td>62.1</td></tr>\n</table>\n\n<h3>Bangle Size Guide</h3>\n<p>Measure the widest part of your hand when thumb and little finger are brought together.</p>\n\n<h3>Necklace Length Guide</h3>\n<ul>\n<li><strong>14-16\" (Choker):</strong> Sits at the base of neck</li>\n<li><strong>16-18\" (Princess):</strong> Sits at collarbone - most versatile</li>\n<li><strong>20-24\" (Matinee):</strong> Falls below collarbone</li>\n<li><strong>28-36\" (Opera):</strong> Falls at or below bust line</li>\n</ul>', 'Find the perfect fit with our comprehensive jewelry size guide for rings, bangles, necklaces, and more.', 'Size Guide - ShreeJi Jewelry', 'Find the perfect fit with our comprehensive jewelry size guide for rings, bangles, necklaces, and more.', '[\"size guide\",\"ring size\",\"jewelry sizing\",\"measurements\"]', 'guide', 1, 0, 12, NULL, NULL, NULL, NULL, '2025-07-01 19:38:16', '2025-07-01 19:38:16', '2025-07-01 19:38:16'),
(6, 'Jewelry Care Guide', 'jewelry-care', '<h2>Jewelry Care Guide</h2>\n<p>Keep your precious jewelry sparkling for generations.</p>\n\n<h3>Gold Jewelry Care</h3>\n<h4>Do\'s:</h4>\n<ul>\n<li>Clean with warm soapy water</li>\n<li>Use a soft-bristled toothbrush</li>\n<li>Dry thoroughly after cleaning</li>\n<li>Store in individual pouches</li>\n<li>Remove before swimming</li>\n</ul>\n\n<h4>Don\'ts:</h4>\n<ul>\n<li>Use harsh chemicals or bleach</li>\n<li>Wear while exercising</li>\n<li>Store with other metals</li>\n<li>Use ultrasonic cleaners on gemstones</li>\n<li>Expose to perfumes directly</li>\n</ul>\n\n<h3>Silver Jewelry Care</h3>\n<h4>Do\'s:</h4>\n<ul>\n<li>Polish regularly with silver cloth</li>\n<li>Store in anti-tarnish pouches</li>\n<li>Wear frequently to prevent tarnishing</li>\n<li>Clean with silver-specific cleaners</li>\n<li>Keep away from humidity</li>\n</ul>\n\n<h3>Gemstone Care</h3>\n<ul>\n<li><strong>Diamond:</strong> Warm soapy water, soft brush</li>\n<li><strong>Ruby/Sapphire:</strong> Warm water, avoid sudden temperature changes</li>\n<li><strong>Emerald:</strong> Gentle cleaning, avoid ultrasonic cleaners</li>\n<li><strong>Pearl:</strong> Soft cloth only, avoid chemicals</li>\n</ul>\n\n<h3>Storage Tips</h3>\n<ul>\n<li>Store each piece separately in soft pouches</li>\n<li>Keep in a cool, dry place away from sunlight</li>\n<li>Use jewelry boxes with individual compartments</li>\n<li>Regular inspection for loose stones or damaged clasps</li>\n</ul>', 'Learn how to properly care for your precious jewelry with our comprehensive care guide and maintenance tips.', 'Jewelry Care Guide - ShreeJi Jewelry', 'Learn how to properly care for your precious jewelry with our comprehensive care guide and maintenance tips.', '[\"jewelry care\",\"maintenance\",\"cleaning\",\"storage\"]', 'guide', 1, 0, 13, NULL, NULL, NULL, NULL, '2025-07-01 19:38:16', '2025-07-01 19:38:16', '2025-07-01 19:38:16'),
(7, 'Warranty Information', 'warranty', '<h2>Warranty Information</h2>\n<p>Comprehensive protection for your precious jewelry investment.</p>\n\n<h3>Warranty Coverage</h3>\n<h4>What\'s Covered:</h4>\n<ul>\n<li>Manufacturing defects in materials</li>\n<li>Faulty craftsmanship issues</li>\n<li>Loose or broken prongs</li>\n<li>Defective clasps or closures</li>\n<li>Stone setting problems</li>\n<li>Metal discoloration (plated items)</li>\n</ul>\n\n<h4>Not Covered:</h4>\n<ul>\n<li>Normal wear and tear</li>\n<li>Damage from misuse or abuse</li>\n<li>Loss or theft</li>\n<li>Damage from chemicals</li>\n<li>Scratches on metal surfaces</li>\n<li>Alterations by third parties</li>\n</ul>\n\n<h3>Warranty Terms</h3>\n<ul>\n<li><strong>Gold Jewelry (14K+):</strong> 1 Year</li>\n<li><strong>Silver Jewelry:</strong> 1 Year</li>\n<li><strong>Platinum Jewelry:</strong> 1 Year</li>\n<li><strong>Diamond Jewelry:</strong> 1 Year (Setting & mounting issues)</li>\n<li><strong>Gemstone Jewelry:</strong> 6 Months (Setting issues only)</li>\n<li><strong>Fashion Jewelry:</strong> 3 Months</li>\n</ul>\n\n<h3>Warranty Claim Process</h3>\n<ol>\n<li>Contact our customer service team at <a href=\"tel:+************\">+91-96174-59017</a> or <a href=\"mailto:<EMAIL>\"><EMAIL></a></li>\n<li>Provide purchase details and issue description</li>\n<li>Send item for inspection (prepaid label provided)</li>\n<li>Item repaired and returned within 7-10 business days</li>\n</ol>\n\n<h3>Contact Warranty Department</h3>\n<p><strong>Phone:</strong> <a href=\"tel:+************\">+91-96174-59017</a><br>\n<strong>Email:</strong> <a href=\"mailto:<EMAIL>\"><EMAIL></a><br>\n<strong>WhatsApp:</strong> <a href=\"https://wa.me/************\" target=\"_blank\">+91-96174-59017</a></p>\n\n<h3>Lifetime Services</h3>\n<p>Beyond warranty period, we offer:</p>\n<ul>\n<li>Complimentary cleaning service</li>\n<li>Annual quality inspection</li>\n<li>Minor repairs at discounted rates</li>\n<li>Professional resizing services</li>\n</ul>', 'Learn about our comprehensive warranty coverage, terms, and conditions for all ShreeJi jewelry purchases.', 'Warranty Information - ShreeJi Jewelry', 'Learn about our comprehensive warranty coverage, terms, and conditions for all ShreeJi jewelry purchases.', '[\"warranty\",\"guarantee\",\"jewelry warranty\",\"coverage\"]', 'policy', 1, 0, 14, NULL, NULL, NULL, NULL, '2025-07-01 19:38:16', '2025-07-01 19:38:16', '2025-07-01 19:38:16'),
(8, 'Privacy Policy', 'privacy-policy', '<h2>Privacy Policy</h2>\n<p>Your privacy and data security are our top priorities.</p>\n\n<h3>Information We Collect</h3>\n<h4>Personal Information:</h4>\n<ul>\n<li>Name, email address, and phone number</li>\n<li>Billing and shipping addresses</li>\n<li>Payment information (processed securely)</li>\n<li>Account credentials and preferences</li>\n<li>Purchase history and order details</li>\n</ul>\n\n<h4>Automatically Collected Information:</h4>\n<ul>\n<li>IP address and browser information</li>\n<li>Device type and operating system</li>\n<li>Pages visited and time spent on site</li>\n<li>Cookies and tracking technologies</li>\n</ul>\n\n<h3>How We Use Your Information</h3>\n<ul>\n<li>Process and fulfill orders</li>\n<li>Provide customer support</li>\n<li>Send marketing communications (with consent)</li>\n<li>Improve our services and website</li>\n<li>Prevent fraud and ensure security</li>\n</ul>\n\n<h3>Information Sharing</h3>\n<p>We do not sell, trade, or rent your personal information to third parties. We may share information with:</p>\n<ul>\n<li>Service providers (payment processors, shipping companies)</li>\n<li>Legal authorities when required by law</li>\n<li>Business partners for legitimate business purposes</li>\n</ul>\n\n<h3>Data Security</h3>\n<ul>\n<li>SSL/TLS encryption for data transmission</li>\n<li>Secure servers with access controls</li>\n<li>Regular security audits and updates</li>\n<li>Staff training on data protection</li>\n</ul>\n\n<h3>Your Rights</h3>\n<ul>\n<li>Access your personal information</li>\n<li>Correct inaccurate information</li>\n<li>Request deletion of your data</li>\n<li>Opt-out of marketing communications</li>\n</ul>\n\n<h3>Contact Us About Privacy</h3>\n<p>If you have questions about this Privacy Policy or our data practices, please contact us:</p>\n<p><strong>Privacy Officer:</strong> <a href=\"mailto:<EMAIL>\"><EMAIL></a><br>\n<strong>Phone:</strong> <a href=\"tel:+************\">+91-96174-59017</a><br>\n<strong>Address:</strong> 123 Jewelry Street, Mumbai, Maharashtra 400001, India</p>', 'Read our privacy policy to understand how we collect, use, and protect your personal information at ShreeJi Jewelry.', 'Privacy Policy - ShreeJi Jewelry', 'Read our privacy policy to understand how we collect, use, and protect your personal information at ShreeJi Jewelry.', '[\"privacy policy\",\"data protection\",\"personal information\"]', 'legal', 1, 0, 20, NULL, NULL, NULL, NULL, '2025-07-01 19:38:16', '2025-07-01 19:38:16', '2025-07-01 19:38:16'),
(9, 'Terms of Service', 'terms-of-service', '<h2>Terms of Service</h2>\n<p>Please read these terms carefully before using our services.</p>\n\n<h3>Agreement to Terms</h3>\n<p>By accessing or using our website, you agree to be bound by these Terms of Service. If you disagree with any part of these terms, you may not access our website or use our services.</p>\n\n<h3>Use of Our Service</h3>\n<h4>Permitted Uses:</h4>\n<ul>\n<li>Browse and purchase jewelry products</li>\n<li>Create and manage your account</li>\n<li>Contact customer service for support</li>\n<li>Leave reviews and feedback</li>\n</ul>\n\n<h4>Prohibited Uses:</h4>\n<ul>\n<li>Violate any applicable laws or regulations</li>\n<li>Transmit harmful or malicious code</li>\n<li>Attempt unauthorized access to our systems</li>\n<li>Interfere with or disrupt our service</li>\n<li>Engage in fraudulent activities</li>\n</ul>\n\n<h3>Account Terms</h3>\n<ul>\n<li>You must provide accurate and complete information</li>\n<li>You must be at least 18 years old to create an account</li>\n<li>You are responsible for maintaining account security</li>\n<li>One account per person is allowed</li>\n</ul>\n\n<h3>Purchase Terms</h3>\n<ul>\n<li>All orders subject to acceptance</li>\n<li>Prices may change without notice</li>\n<li>Payment required before shipping</li>\n<li>30-day return policy applies</li>\n</ul>\n\n<h3>Intellectual Property</h3>\n<p>All content on our website is owned by ShreeJi Jewelry and protected by copyright and trademark laws. You may not use, reproduce, or distribute our content without written permission.</p>\n\n<h3>Limitation of Liability</h3>\n<p>To the maximum extent permitted by law, ShreeJi Jewelry shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of our service.</p>\n\n<h3>Governing Law</h3>\n<p>These Terms shall be governed by the laws of India. Any disputes shall be subject to the exclusive jurisdiction of the courts in Mumbai, India.</p>', 'Read our terms of service to understand the rules and regulations for using ShreeJi Jewelry website and services.', 'Terms of Service - ShreeJi Jewelry', 'Read our terms of service to understand the rules and regulations for using ShreeJi Jewelry website and services.', '[\"terms of service\",\"terms and conditions\",\"legal\"]', 'legal', 1, 0, 21, NULL, NULL, NULL, NULL, '2025-07-01 19:38:16', '2025-07-01 19:38:16', '2025-07-01 19:38:16'),
(10, 'Cookie Policy', 'cookie-policy', '<h2>Cookie Policy</h2>\n<p>Understanding how we use cookies to improve your experience.</p>\n\n<h3>What Are Cookies?</h3>\n<p>Cookies are small text files stored on your device when you visit our website. They help us provide you with a better browsing experience by remembering your preferences and analyzing how you use our site.</p>\n\n<h3>Types of Cookies We Use</h3>\n<h4>Essential Cookies:</h4>\n<ul>\n<li>Authentication and security cookies</li>\n<li>Shopping cart functionality</li>\n<li>Session management</li>\n<li>CSRF protection</li>\n</ul>\n\n<h4>Functional Cookies:</h4>\n<ul>\n<li>Language and region preferences</li>\n<li>Currency selection</li>\n<li>Recently viewed products</li>\n<li>Wishlist items</li>\n</ul>\n\n<h4>Analytics Cookies:</h4>\n<ul>\n<li>Page views and user journeys</li>\n<li>Time spent on pages</li>\n<li>Popular products and categories</li>\n<li>Device and browser information</li>\n</ul>\n\n<h4>Marketing Cookies:</h4>\n<ul>\n<li>Personalized product recommendations</li>\n<li>Retargeting advertisements</li>\n<li>Social media integration</li>\n<li>Conversion tracking</li>\n</ul>\n\n<h3>Third-Party Cookies</h3>\n<p>We use services from trusted third parties:</p>\n<ul>\n<li><strong>Google Analytics:</strong> Website analytics</li>\n<li><strong>Facebook Pixel:</strong> Social media marketing</li>\n<li><strong>Razorpay:</strong> Payment processing</li>\n</ul>\n\n<h3>Managing Cookies</h3>\n<p>You can control cookies through:</p>\n<ul>\n<li>Your browser settings</li>\n<li>Our cookie consent banner</li>\n<li>Third-party opt-out tools</li>\n</ul>\n\n<h3>Impact of Disabling Cookies</h3>\n<p>Disabling certain cookies may affect your experience:</p>\n<ul>\n<li>You may need to re-enter information repeatedly</li>\n<li>Some features may not work properly</li>\n<li>Shopping cart may not function correctly</li>\n<li>Less relevant content and advertisements</li>\n</ul>\n\n<h3>Questions About Cookies</h3>\n<p>If you have questions about our use of cookies, please contact us:</p>\n<p><strong>Privacy Team:</strong> <a href=\"mailto:<EMAIL>\"><EMAIL></a><br>\n<strong>Phone:</strong> <a href=\"tel:+************\">+91-96174-59017</a></p>', 'Learn about how we use cookies and similar technologies to enhance your browsing experience on ShreeJi Jewelry website.', 'Cookie Policy - ShreeJi Jewelry', 'Learn about how we use cookies and similar technologies to enhance your browsing experience on ShreeJi Jewelry website.', '[\"cookie policy\",\"cookies\",\"tracking\",\"privacy\"]', 'legal', 1, 0, 22, NULL, NULL, NULL, NULL, '2025-07-01 19:38:16', '2025-07-01 19:38:16', '2025-07-01 19:38:16');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `sku` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `short_description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `sale_price` decimal(10,2) DEFAULT NULL,
  `stock_quantity` int(11) NOT NULL DEFAULT 0,
  `manage_stock` tinyint(1) NOT NULL DEFAULT 1,
  `in_stock` tinyint(1) NOT NULL DEFAULT 1,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `status` enum('active','inactive','draft') NOT NULL DEFAULT 'active',
  `images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`images`)),
  `specifications` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specifications`)),
  `sizes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`sizes`)),
  `weight` decimal(8,2) DEFAULT NULL,
  `metal_type` varchar(255) DEFAULT NULL,
  `metal_purity` varchar(255) DEFAULT NULL,
  `stone_type` varchar(255) DEFAULT NULL,
  `stone_weight` decimal(8,2) DEFAULT NULL,
  `certification` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `category_id`, `name`, `slug`, `sku`, `description`, `short_description`, `price`, `sale_price`, `stock_quantity`, `manage_stock`, `in_stock`, `is_featured`, `status`, `images`, `specifications`, `sizes`, `weight`, `metal_type`, `metal_purity`, `stone_type`, `stone_weight`, `certification`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 3, 'Black & Gold Traditional Dangler', 'black-gold-traditional-dangler', 'ER001', '<p>Stylish black and gold dangler earrings with kundan and floral detailing — perfect for festive and ethnic looks.</p>', NULL, 1460.00, 876.00, 4, 1, 1, 0, 'active', '[\"products\\/qPJcorxTgRd5M7lLltETciCZhwa5Rg3y7v95D2ob.jpg\"]', '{\"description\":\"{\\r\\n  \\\"Packaging\\\": \\\"Shipped in a soft velvet pouch with a box\\\",\\r\\n  \\\"Care Instructions\\\": \\\"Avoid water, perfume, and chemicals. Store in a dry box.\\\",\\r\\n  \\\"Style\\\": \\\"Traditional, Ethnic, Dangler\\\",\\r\\n  \\\"Occasion\\\": \\\"Festive, Wedding, Party\\\",\\r\\n  \\\"Handmade\\\": true,\\r\\n  \\\"Lightweight\\\": true,\\r\\n  \\\"Closure\\\":\\u00a0\\\"Push\\u00a0back\\\"\\r\\n}\"}', NULL, 32.00, 'gold', '18k', NULL, 0.50, NULL, 0, '2025-06-18 04:10:19', '2025-08-24 08:21:29'),
(2, 3, 'Antique Dual-Tone Kundan Jhumka Earrings with Red Enamel Top', 'antique-dual-tone-kundan-jhumka-earrings-with-red-enamel-top', 'SKU-089235', '<p>Elegant dual-tone jhumka earrings featuring red kundan top, antique silver engravings, and pearl beaded drops ideal for festive and bridal looks. These handcrafted earrings showcase traditional artistry with modern appeal, perfect for special occasions.</p>', 'Elegant dual-tone jhumka earrings featuring red kundan top, antique silver engravings, and pearl beaded drops  ideal for festive and bridal looks.', 1800.00, 698.00, 10, 1, 1, 1, 'active', '[\"products\\/v3KrUhj3MO1AmaaLDhk7FKxn6mLiK0reF8vs2HpT.jpg\"]', '{\"Packaging\":\"Delivered in a premium gift box\",\"Care Instructions\":\"Store in a dry place, avoid water and perfume\",\"Style\":\"Traditional, Antique Finish, Statement Jhumka\",\"Occasion\":\"Wedding, Festive, Cultural Events\",\"Handmade\":\"true\",\"Closure\":\"Push back with secure grip\",\"Colors\":\"Red,Gold,Antique Silver\"}', NULL, 38.00, NULL, NULL, 'Artificial Kundan, Faux Pearls, Enamel', 1.10, NULL, 1, '2025-06-18 05:23:34', '2025-09-08 13:34:29'),
(3, 3, 'Traditional Multi-Layered Jhoomar Passa with Jhumki Drops', 'traditional-multi-layered-jhoomar-passa-with-jhumki-drops', 'SKU-624687', '<p>Handcrafted gold-toned triple-layered passa with delicate jhumki and pearl drop details perfect for bridal or festive styling. This traditional hair accessory features intricate floral enamel design with central jhumki motif.</p>', 'Handcrafted gold-toned triple-layered passa with delicate jhumki and pearl drop details  perfect for bridal or festive styling.', 1799.00, 895.00, 15, 1, 1, 0, 'active', '[\"products\\/UIAxHbkdsvYw03Ee0EveyrSVdHwU3zDWVy0DC1Hm.jpg\"]', '{\"Packaging\":\"Elegant box packaging with soft padding\",\"Care Instructions\":\"Keep away from water and perfumes. Store in a dry place.\",\"Style\":\"Traditional, Bridal, Side Hair Accessory\",\"Occasion\":\"Wedding, Mehendi, Festive\",\"Handmade\":\"true\",\"Chain Layers\":\"3\",\"Main Motif\":\"Floral Enamel Design with Central Jhumki\"}', '[\"One Size\"]', 42.00, NULL, NULL, 'Faux Pearls, Artificial Stones', 0.90, 'Not certified', 2, '2025-06-18 05:23:52', '2025-07-11 11:05:18'),
(4, 3, 'Contemporary Kundan Tassel Earrings with Pink Enamel', 'contemporary-kundan-tassel-earrings-with-pink-enamel', 'SKU-537318', '<p>Bold and trendy kundan earrings featuring pink enamel detailing, golden ghungroo charms, and long tassel chains. Perfect statement piece for Indo-Western and festive occasions.</p>', 'Bold and trendy kundan earrings featuring pink enamel detailing, golden ghungroo charms, and long tassel chains.', 1999.00, 585.00, 20, 1, 1, 0, 'active', '[\"products\\/fwBwn07Lql4Bk7cvpDPlkcfB78BYrtgjhppdQ8Ga.jpg\"]', '{\"Packaging\":\"Shipped in a premium cotton-lined box\",\"Care Instructions\":\"Avoid water, perfume, and heat. Store separately.\",\"Style\":\"Statement, Indo-Western, Festive\",\"Occasion\":\"Sangeet, Party, Wedding\",\"Handmade\":\"true\",\"Features\":\"[object Object]\",\"Closure\":\"Push back with stopper\"}', '[\"One Size\"]', 28.00, NULL, NULL, 'Artificial Kundan, Enamel', 0.60, 'Not certified', 3, '2025-06-18 05:24:08', '2025-07-22 05:25:28'),
(5, 3, 'Dual Tone Kundan Jhumka Earrings', 'dual-tone-kundan-jhumka-earrings', 'SKU-947948', '<p>Traditional jhumka earrings featuring floral Kundan work and golden chain tassels. These ethnic earrings showcase dual-tone finish with gold and red Kundan detailing, perfect for wedding and festive occasions.</p>', 'Traditional jhumka earrings featuring floral Kundan work and golden chain tassels.', 1599.00, 852.00, 30, 1, 1, 0, 'active', '[\"products\\/org8zzh5740LVgYBMgWjphKkaBnnk9JMOrGkuDOO.jpg\"]', '{\"Style\":\"Ethnic\\/Traditional\",\"Closure\":\"Push Back\",\"Occasion\":\"Wedding, Festive, Party Wear\",\"Finish\":\"Dual-tone (Gold and Red with Kundan)\"}', '[\"Free Size\"]', 38.00, NULL, NULL, 'Kundan', NULL, 'NA', 4, '2025-06-18 05:24:23', '2025-07-04 01:14:29'),
(6, 6, 'Traditional Lakshmi Gold-Plated Necklace & Earrings Set', 'traditional-lakshmi-gold-plated-necklace-earrings-set', 'SKU-969017', '<p>Elegant temple-style necklace set featuring intricate Lakshmi motifs and hanging bead chains, ideal for festive and traditional occasions. This beautiful set includes matching earrings with chain drops.</p>', 'Elegant temple-style necklace set featuring intricate Lakshmi motifs and hanging bead chains, ideal for festive and traditional occasions.', 2999.00, 885.00, 12, 1, 1, 0, 'active', '[\"products\\/OwzTkittTB0AtLGNlneAtrN2kB6cYK5mxsI6s8y1.jpg\"]', '{\"style\":\"Temple Jewelry\",\"theme\":\"Lakshmi Motif\",\"occasion\":\"Festive, Wedding, Traditional\",\"closure_type\":\"Adjustable Dori\",\"earring_type\":\"Stud with Chain Drops\",\"finish\":\"Antique Gold Polish\"}', '[\"One size fits all\"]', 60.00, NULL, NULL, 'Synthetic Ruby (Red)', 0.50, 'Not certified / In-house QC', 5, '2025-06-18 05:24:44', '2025-07-11 11:05:00'),
(7, 6, 'Antique Leaf Motif Necklace & Pearl Drop Earrings Set', 'antique-leaf-motif-necklace-pearl-drop-earrings-set', 'SKU-271655', '<p>Traditional gold-tone necklace set with detailed leaf motifs and pearl drop accents, ideal for ethnic wear. Features antique matte gold finish with synthetic pearls and green beads.</p>', 'Traditional gold-tone necklace set with detailed leaf motifs and pearl drop accents, ideal for ethnic wear.', 1100.00, 699.00, 17, 1, 1, 0, 'active', '[\"products\\/XRd6Ijh9Yatj2HPACy0d9MvS8FobeBYe81oJBeEi.jpg\"]', '{\"style\":\"Antique Traditional\",\"design_motif\":\"Leaf Pattern\",\"occasion\":\"Festive, Traditional, Religious\",\"closure_type\":\"Adjustable Hook\",\"earring_type\":\"Stud with Pearl Drop\",\"finish\":\"Antique Matte Gold\",\"main_stone_shape\":\"Round Pearl\"}', '[\"One size fits all\"]', 45.00, NULL, NULL, 'Synthetic Pearls, Green Beads', 0.60, 'Not certified / In-house QC', 6, '2025-06-18 05:24:59', '2025-09-08 13:35:02'),
(8, 5, 'Elegant White Stone Necklace Set with Dangle Earrings', 'elegant-white-stone-necklace-set-with-dangle-earrings', 'SKU-091061', '<p>A modern and minimalistic necklace set featuring geometric-cut white stones, perfect for parties and evening occasions. Features rose gold glossy finish with cubic zirconia stones.</p>', 'A modern and minimalistic necklace set featuring geometric-cut white stones, perfect for parties and evening occasions.', 1200.00, 899.00, 22, 1, 1, 0, 'active', '[\"products\\/9rH3PalguWNQVgEeCkV7dqAK3CGpuOCY7pcwLGI0.jpg\"]', '{\"style\":\"Modern Minimalist\",\"design_motif\":\"Geometric White CZ Stones\",\"earring_type\":\"Dangle\",\"occasion\":\"Party, Evening, Bridal\",\"finish\":\"Rose Gold Glossy\",\"stone_color\":\"White\",\"closure_type\":\"Lobster Hook\",\"main_stone_shape\":\"Square, Pear, Round Mix\"}', '[\"One size\"]', 46.00, NULL, NULL, 'Cubic Zirconia (White)', 2.10, 'Not certified / In-house QC', 7, '2025-06-18 05:25:15', '2025-07-14 11:04:04'),
(9, 7, 'Traditional Kundan Bridal Nath with Pearl Chain', 'traditional-kundan-bridal-nath-with-pearl-chain', 'SKU-297077', '<p>Elegant bridal nath crafted in kundan work with pearl chain and colorful bead accents. Features teardrop design with kundan and beads, perfect for wedding and cultural events.</p>', 'Elegant bridal nath crafted in kundan work with pearl chain and colorful bead accents', 1299.00, 399.00, 8, 1, 1, 0, 'active', '[\"products\\/rG34YipR6vwbspD1y3d1FDtA2SPcC4Oo5QxNW6sC.jpg\"]', '{\"style\":\"Traditional Bridal\",\"design_motif\":\"Teardrop with Kundan and Beads\",\"chain_type\":\"Pearl Beaded Side Chain\",\"occasion\":\"Wedding, Festivals, Cultural Events\",\"finish\":\"22K Gold Plated\",\"closure_type\":\"Hook\",\"main_stone_shape\":\"Teardrop\"}', '[\"Free Size\"]', 18.00, NULL, NULL, 'Kundan, Synthetic Ruby & Emerald Beads, Pearls', 0.50, 'Not certified / In-house QC', 8, '2025-06-18 05:25:30', '2025-07-08 03:43:34'),
(10, 3, 'Golden Forest Dangle earrings', 'golden-forest-dangle-earrings', 'EAR-FRSTDZ-001', '<p>These artisan-crafted earrings showcase a breathtaking nature-inspired scene with a graceful deer and a tree, enclosed in a golden circular frame. The design is accented by a lustrous shell-like stone at the top and a polished black gemstone for a touch of contrast. Perfect for nature lovers, cultural events, or premium gifting, these earrings bring together storytelling and sophistication.</p>', 'Elegant gold-toned dangle earrings featuring a scenic deer and tree motif, set in a circular frame with glossy black and pearl-', 999.00, 390.00, 10, 1, 1, 0, 'active', '[\"products\\/ihZElyYEuxNsjmTSbH0enULxILHlCxmJRl7zRFyd.jpg\"]', '{\"Closure Type\":\"Hook\",\"Finish\":\"Matte Gold\",\"Theme\":\"Nature, Forest, Wildlife\",\"Design\":\"Laser Cut and Hand-Assembled\",\"Occasion\":\"Festive, Wedding, Party Wear\",\"Gender\":\"Women\",\"Handmade\":\"true\"}', '[\"Free size\"]', 14.50, NULL, '18k', 'Imitation Onyx, Shell Pearl', 2.50, 'Not Certified (Fashion Jewelry)', 0, '2025-06-30 04:59:07', '2025-07-19 02:25:41'),
(12, 3, 'Antique Peacock Motif Earrings with Pearl Drops', 'antique-peacock-motif-earrings-with-pearl-drops', 'PEACOCK-GLD-6559', '<p>Step into royal elegance with these antique peacock motif earrings. Crafted in a heart-shaped gold-tone base, the design showcases intricate twin peacock engravings framed with delicate borders and beaded pearls. A central pink stone adds a soft contrast, enhancing the overall richness of the piece. The top features a carved floral disc, giving the earrings a temple jewellery vibe. Perfect for bridal wear, festive occasions, or traditional attire, these statement earrings reflect heritage craftsmanship in a modern, wearable form.</p>', 'Antique gold-tone earrings featuring peacock motifs, pearl beading, and a soft pink central stone for regal elegance.', 1250.00, 340.00, 23, 1, 1, 0, 'active', '[\"products\\/O71n7g7TlJsupI7EMhk0Ol8TVWOFAtAGin9Mgwjc.jpg\"]', '{\"closure\":\"Push Back\",\"design_style\":\"Temple \\/ Heritage\",\"motif\":\"Peacock and Floral\",\"color\":\"Gold with Pink Stone and White Pearls\",\"occasion\":\"Wedding, Festive, Traditional Events\",\"handcrafted\":\"true\",\"finish\":\"Antique Matte Gold\",\"origin\":\"India\"}', '[\"NA\"]', 20.00, NULL, NULL, 'Synthetic Pink Stone, Faux Pearls', 10.00, 'Na', 0, '2025-07-11 03:04:42', '2025-08-23 11:52:30'),
(13, 6, 'Antique Floral Gold Necklace Set with Earrings', 'antique-floral-gold-necklace-set-with-earrings', 'FLR-SET-9580', '<p>Bring a timeless charm to your ethnic ensemble with this beautifully crafted antique gold floral necklace set. Designed with an intricate daisy pattern, the set features flower motifs adorned with ruby-pink synthetic stones at the center. Hanging beads add a graceful movement, while the antique finish gives it a regal look. The matching earrings echo the necklace design for a complete and coordinated appearance. Ideal for festive celebrations, weddings, and traditional wear, this set adds elegance and warmth to your jewellery collection.</p>', 'A handcrafted floral motif necklace set with matching earrings in antique gold finish and ruby-colored accents.', 1250.00, 585.00, 14, 1, 1, 0, 'active', '[\"products\\/IM2nMdsUCF4uOZHbBdtfNIEoWhDwlZ7N4UoRZFXs.jpg\"]', '{\"closure\":\"Adjustable Thread Back\",\"design_style\":\"Traditional \\/ Floral Motif\",\"set_includes\":\"1 Necklace + 2 Earrings\",\"colors\":\"Antique Gold,Ruby Pink\",\"occasion\":\"Festive, Wedding, Traditional Events\",\"handcrafted\":\"true\",\"origin\":\"India\",\"finish\":\"Matte Antique\"}', '[\"Free size\"]', 52.00, NULL, NULL, 'Synthetic Ruby-Colored Stones', 10.00, 'NA', 0, '2025-07-11 11:03:40', '2025-09-08 13:35:02'),
(14, 7, 'Elegant Pearl Chain Nath with Kundan Accent', 'elegant-pearl-chain-nath-with-kundan-accent', 'NTH-KNDPRL-7284', '<p>This delicate handcrafted nath showcases a minimalist circular design, highlighted by a clear kundan stone and a single drop pearl. Two elegantly draped side chains—one gold link and one with small white pearls—add a regal and graceful effect, making it ideal for bridal wear or festive occasions. The nath is lightweight, comfortable to wear, and designed without piercing, perfect for a no-fuss traditional statement look.</p>', 'Gold-tone nath with kundan centerpiece and dual pearl-gold chain drape, perfect for ethnic and bridal looks.', 699.00, 280.00, 19, 1, 1, 0, 'active', '[\"products\\/QdRAQ0p3HsX7m4fRWWtplLJ7AReOgFzQ95K318V7.png\"]', '{\"wear_type\":\"Non-Pierced\",\"closure\":\"Clip-On\",\"design_style\":\"Traditional \\/ Bridal\",\"features\":\"Lightweight,Dual Chain,Pearl Accents\",\"colors\":\"Gold,White\",\"occasion\":\"Wedding, Festive, Cultural Events\",\"handcrafted\":\"true\",\"origin\":\"India\",\"finish\":\"Matte Gold-Tone\"}', '[\"NA\"]', 10.00, NULL, NULL, 'Synthetic Kundan, Faux Pearls', NULL, 'NA', 0, '2025-07-11 11:12:10', '2025-07-11 11:12:10'),
(15, 3, 'Royal Red Stone Dome Earrings with Pearl Fringe', 'royal-red-stone-dome-earrings-with-pearl-fringe', 'DOME-REDPRL-8828', '<p>These stunning handcrafted earrings blend royal elegance with traditional aesthetics. The design features a central dome motif with intricate mesh-style carvings surrounded by a beaded frame. A bold red oval stone crowns the top, while a cascade of faux pearls adds movement and charm to the lower edge. Lightweight yet statement-worthy, these earrings are ideal for festive events, weddings, or as a standout piece with ethnic attire.</p>', 'Gold dome-style earrings with bold red stones and pearl drop detailing for a regal ethnic touch.', 599.00, 350.00, 9, 1, 1, 0, 'active', '[\"products\\/8RyJY27TacK3UQTZJcj8ZObAKsnWb7T1iwjZOxVS.jpg\"]', '{\"closure\":\"Push Back\",\"design_style\":\"Traditional \\/ Dome Motif\",\"features\":\"Red Accent Stone,Pearl Drop Fringe\",\"color\":\"Gold with Red and White\",\"ideal_for\":\"Women, Girls\",\"occasion\":\"Wedding, Festivals, Ethnic Celebrations\",\"handcrafted\":\"true\",\"origin\":\"India\",\"finish\":\"Glossy Antique Gold\"}', '[\"NA\"]', 10.00, NULL, NULL, 'Synthetic Red Stone, Faux Pearls', 10.00, 'NA', 0, '2025-07-11 11:20:57', '2025-07-11 11:20:57'),
(16, 3, 'Peacock Blue Drop Earrings with Pearl Tassels', 'peacock-blue-drop-earrings-with-pearl-tassels', 'PCKBL-PRL-7921', '<p>Inspired by royal Indian heritage, these statement earrings are crafted in a traditional rectangular peacock motif at the top, symbolizing grace and elegance. Below the centerpiece lie three rich blue circular stones that add a pop of color and contrast beautifully with the gold base. The lower fringe is adorned with delicately strung white pearls and teardrop-shaped beads, offering movement and classic charm. These earrings are a must-have for festive occasions and traditional wear.</p>', 'Elegant gold-tone earrings with carved peacock design, blue accents, and cascading pearl tassels.', 1250.00, 640.00, 9, 1, 1, 0, 'active', '[\"products\\/OZmWl8mfuGn7QcGCXGbVWCQyOdrClrZrJBsTZeXP.jpg\"]', '{\"design_motif\":\"Peacock\",\"features\":\"Blue Center Stones,Pearl Tassels,Carved Top\",\"colors\":\"Gold,Blue,White\",\"ideal_for\":\"Women\",\"occasion\":\"Festivals, Weddings, Ethnic Events\",\"handcrafted\":\"true\",\"origin\":\"India\",\"finish\":\"Textured Matte Gold\"}', '[\"NA\"]', 28.00, NULL, NULL, 'Synthetic Blue Stone, Faux Pearls', 10.00, 'NA', 0, '2025-07-11 11:26:51', '2025-08-24 08:25:08'),
(18, 3, 'Antique Oxidized Dome Jhumka with Pearl Layers', 'antique-oxidized-dome-jhumka-with-pearl-layers', 'OXDJ-PJ-2461', '<p>This pair of oxidized jhumka earrings brings together classic charm and intricate detailing. Featuring dome-shaped patterns in oxidized gold finish, the design is elevated with tiered white pearl drops that add graceful movement. The teardrop stud top is embellished with green and golden accents along with red enamel work, enhancing its festive flair. Ideal for pairing with lehengas, sarees, or traditional suits, these earrings are a timeless pick for weddings and festive occasions.</p>', 'Oxidized dome jhumkas featuring layered pearl strings and colorful enamel top studs, perfect for ethnic charm.', 1600.00, 852.00, 0, 1, 0, 0, 'active', '[\"products\\/Gh8JR4FGg8ADVxrvWLy5qq3k0ZsIK2PcH10MRicf.jpg\"]', '{\"style\":\"Oxidized Dome Jhumka\",\"top_stone\":\"Green Bead with Kundan and Red Enamel\",\"base_design\":\"Engraved Oxidized Dome\",\"dangling_elements\":\"Multi-layer Faux Pearls\",\"ideal_for\":\"Women\",\"occasion\":\"Festive Wear, Weddings, Traditional Events\",\"handcrafted\":\"true\",\"origin\":\"India\",\"finish\":\"Antique Gold \\/ Oxidized\"}', '[\"Free size\"]', 30.00, NULL, NULL, 'Faux Pearls, Enamel', 10.00, 'NA', 0, '2025-07-11 11:42:55', '2025-08-24 08:14:20'),
(19, 5, 'Elegant Gold-Plated AD Necklace Set', 'elegant-gold-plated-ad-necklace-set', 'GADN-SET-3071', '<p>Add timeless sparkle to your special moments with this elegant gold-plated American Diamond necklace set. The necklace features alternating square-cut and pear-shaped AD stones set in a fine gold-polish base, offering a luxurious and versatile design. Matching stud earrings complete the look, making this set ideal for festive wear, weddings, and evening celebrations. Lightweight and finely finished, it\'s a must-have for your ethnic or Indo-western wardrobe.</p>', 'Gold-plated necklace set adorned with square-cut and teardrop American Diamonds for a stunning festive look.', 1600.00, 785.00, 10, 1, 1, 0, 'active', '[\"products\\/OxhcCJi1hnPbJLPkqT3Qxz5GGeq5cuCFSb0YkPrR.jpg\"]', '{\"set_type\":\"Necklace with Stud Earrings\",\"stone_shape\":\"Square,Teardrop\",\"metal_finish\":\"High Gloss Gold-Plated\",\"ideal_for\":\"Women\",\"occasion\":\"Festive, Wedding, Party\",\"plating\":\"1 gm gold polish\",\"stone_quality\":\"Premium CZ\\/AD Stones\",\"adjustable_closure\":\"true\",\"handcrafted\":\"true\",\"origin\":\"India\"}', '[\"NA\"]', 40.00, NULL, NULL, 'American Diamond (AD), CZ', 30.00, 'Na', 0, '2025-07-11 11:55:32', '2025-07-11 11:55:32'),
(20, 9, 'Oxidized Silver Floral Jhumka Earrings with Pearls', 'oxidized-silver-floral-jhumka-earrings-with-pearls', 'OX-JHUMKA-2034', '<p>Add a touch of rustic charm and cultural elegance to your ethnic look with these oxidized silver jhumka earrings. Featuring detailed floral engravings and a cascade of faux pearl drops, these earrings bring out traditional craftsmanship in a modern silhouette. Lightweight yet bold, they are ideal for daily wear, festive events, or pairing with cotton and silk sarees for a boho-traditional look.</p>', 'Traditional oxidized silver jhumka earrings featuring intricate floral motifs and pearl drops.', 499.00, 255.00, 7, 1, 1, 0, 'active', '[\"products\\/HrGqWiuxTULWa43Ssgms5BYSFY8RrQvCrKNI8iUl.jpg\"]', '{\"earring_style\":\"Jhumka\",\"metal_finish\":\"Oxidized Antique Silver\",\"ideal_for\":\"Women\",\"occasion\":\"Festive, Daily Wear, Traditional Events\",\"theme\":\"Floral Motif\",\"plating\":\"None (Oxidized)\",\"stone_type\":\"Pearl Beads\",\"handcrafted\":\"true\",\"origin\":\"India\",\"closure_type\":\"Hook\"}', '[\"Na\"]', 25.00, NULL, NULL, 'Faux Pearls', 0.00, 'Na', 0, '2025-07-11 12:05:03', '2025-09-08 13:06:28'),
(21, 9, 'Oxidized Kettle & Cup Dangler Earrings', 'oxidized-kettle-cup-dangler-earrings', 'OX-KETTLE-2042', '<p>Stand out with these statement oxidized dangler earrings featuring a fun and artistic design of a kettle pouring tea into a cup. Made for lovers of all things quirky, these earrings are crafted in antique silver-tone alloy and finished with miniature metallic beads for added charm. Lightweight and eye-catching, they are ideal for college wear, street fashion, or as a conversation starter in casual gatherings.</p>', 'Funky oxidized dangler earrings in the shape of a kettle pouring into a teacup—perfect for bold, artsy looks.', 499.00, 235.00, 15, 1, 1, 0, 'active', '[\"products\\/D2prQoceEhN4A1d8Jc5HJOkzzqvDC6c9sPOv0zWO.jpg\"]', '{\"earring_style\":\"Dangler\",\"theme\":\"Kettle & Cup Quirky Art\",\"metal_finish\":\"Oxidized Antique Silver\",\"ideal_for\":\"Women, College Girls, Fashionistas\",\"occasion\":\"Casual, Art Events, Gifting\",\"plating\":\"None\",\"handcrafted\":\"true\",\"origin\":\"India\",\"closure_type\":\"Hook\"}', '[\"Na\"]', 20.00, NULL, NULL, 'None', NULL, 'NA', 0, '2025-07-11 12:14:10', '2025-07-14 11:03:06'),
(22, 9, 'Oxidized Leaf & Blue Half-Moon Pearl Earrings', 'oxidized-leaf-blue-half-moon-pearl-earrings', 'OX-LF-BLUE-2061', '<p>These elegant oxidized earrings are designed with a unique leaf-shaped top, a captivating blue half-moon enamel centerpiece, and a fringe of delicate pearl beads. The intricate detailing and dual-tone contrast add a graceful ethnic touch. Lightweight and versatile, they are ideal for both traditional and fusion outfits, making them a must-have for your jewellery collection.</p>', 'Chic oxidized earrings featuring a leaf design, blue half-moon center, and pearl hangings — perfect for a trendy ethnic vibe.', 599.00, 295.00, 15, 1, 1, 0, 'active', '[\"products\\/kH25vuHj9CvE6s7ujI2K5X3XznFF6qATJCDdjrNS.jpg\"]', '{\"earring_style\":\"Dangler\",\"design_motif\":\"Leaf and Half-Moon\",\"metal_finish\":\"Oxidized Antique\",\"center_color\":\"Turquoise Blue\",\"beads\":\"Mini Pearl Beads\",\"ideal_for\":\"Women, Girls\",\"occasion\":\"Festive, Office Party, Traditional Wear\",\"handcrafted\":\"true\",\"origin\":\"India\",\"closure_type\":\"Push Back Hook\"}', '[\"NA\"]', 30.00, NULL, NULL, 'Artificial Stone + Imitation Pearls', 10.00, 'Na', 0, '2025-07-11 12:25:23', '2025-07-14 11:02:50'),
(23, 2, 'Twist Hoop Gold-Tone Earrings', 'twist-hoop-gold-tone-earrings', 'TGE-HOOP-002', '<p>Elevate your everyday elegance with these beautifully crafted twist hoop earrings. Featuring a layered, intertwined design in a polished gold-tone, they offer a modern yet classic look suitable for both casual and formal wear. Lightweight and comfortable for daily use, these hoops effortlessly add shine and sophistication to any outfit.</p>', 'Stylish twist hoop earrings with a radiant gold finish – a timeless addition to your collection.', 399.00, 149.00, 9, 1, 1, 0, 'active', '[\"products\\/0D2DByXTqXt6Ki4Ga4goL42uX7oOwDx93hTkDdMl.jpg\"]', '{\"closure_type\":\"Latch Back\",\"finish\":\"High Gloss Gold Polish\",\"style\":\"Hoop, Contemporary\",\"design\":\"Twisted Layered\",\"hypoallergenic\":\"false\",\"occasion\":\"Daily, Festive, Office, Partywear\"}', NULL, 18.00, NULL, NULL, NULL, NULL, 'Na', 0, '2025-07-14 11:57:15', '2025-09-08 13:06:28'),
(24, 2, 'Triple Layered Gold-Toned Hoop Earrings', 'triple-layered-gold-toned-hoop-earrings', 'SKU: GLD-HOOP-TRI-2025', '<p>Elevate your style with these striking triple-layered gold-toned hoop earrings. Designed for the modern woman who appreciates timeless pieces with a twist, these earrings feature a smooth high-polish finish and lightweight feel, making them ideal for daily wear, office looks, or casual outings. The structured design offers a refined edge to your accessory collection.</p>', 'Minimal yet statement-making, these triple-layered gold hoops are the perfect fusion of classic and contemporary.', 499.00, 259.00, 10, 1, 1, 0, 'active', '[\"products\\/PbqcYlPI0fYVSCq5cNwn06eUsyJIdQB3O9eSriJq.jpg\"]', '{\"earring_style\":\"Hoop Earrings\",\"finish\":\"Glossy Gold Polish\",\"metal_base\":\"Brass\",\"ideal_for\":\"Women\",\"closure_type\":\"Push Back\",\"occasion\":\"Casual, Office, Daily Wear\",\"handcrafted\":\"false\",\"origin\":\"India\"}', NULL, 15.00, NULL, NULL, NULL, NULL, 'Na', 0, '2025-07-14 12:04:55', '2025-07-14 12:04:55'),
(25, 2, 'Golden Pearl Embedded Hoop Earrings', 'golden-pearl-embedded-hoop-earrings', 'SKU: EGR-PEARL-HOOP-7139', '<p>Elevate your accessory game with these gorgeous golden pearl hoop earrings. These earrings combine classic charm and modern minimalism, featuring lustrous faux pearls delicately embedded along a textured golden hoop. Their lightweight design and push-back closure ensure comfort for daily wear or special occasions. An ideal pick for weddings, ethnic celebrations, or even work ensembles.</p>', 'Chic golden hoop earrings embellished with glossy pearl beads—timeless elegance for any outfit.', 499.00, 199.00, 10, 1, 1, 0, 'active', '[\"products\\/UVnCfeZXSDyTyyVI81nBCwhynWeShQrYEPOe2nRY.jpg\"]', '{\"style\":\"Hoop\",\"design\":\"Pearl Embedded\",\"finish\":\"Textured Gold\",\"stone_type\":\"Faux Pearls\",\"metal_base\":\"Alloy\",\"plating\":\"Gold\",\"ideal_for\":\"Women\",\"occasion\":\"Festive,Casual,Traditional\",\"closure\":\"Push Back\",\"origin\":\"India\",\"handmade\":\"false\",\"weight\":\"10 grams\"}', NULL, 10.00, NULL, NULL, NULL, NULL, 'Na', 0, '2025-07-14 12:13:05', '2025-07-14 12:13:05'),
(26, 2, 'Minimalist Golden Heart Minimalist Golden Heart Stud Earrings', 'minimalist-golden-heart-minimalist-golden-heart-stud-earrings', 'EGR-HRT-MIN-7148', '<p>Embrace a touch of subtle romance with these minimalist golden heart stud earrings. Crafted with a smooth open-heart design, they are lightweight and versatile—perfect for daily wear or to gift someone special. Their elegant finish adds a soft sparkle, making them a timeless staple for modern jewellery lovers. Designed with a secure push-back closure to ensure comfort and durability.</p>', 'Delicate golden heart-shaped studs designed for effortless everyday charm.', 499.00, 249.00, 10, 1, 1, 0, 'active', '[\"products\\/VcRmMKua9MT57pCSWdypWiafEg9GGLiaVVETuR5K.jpg\"]', '{\"style\":\"Stud\",\"design\":\"Open Heart\",\"finish\":\"Polished Gold\",\"metal_base\":\"Brass Alloy\",\"plating\":\"1 Micron Gold\",\"ideal_for\":\"Women\",\"occasion\":\"Casual,Romantic,Everyday\",\"closure\":\"Push Back\",\"origin\":\"India\",\"handmade\":\"false\",\"weight\":\"5 grams\"}', '[\"Free size\"]', 5.00, NULL, NULL, NULL, NULL, 'Na', 0, '2025-07-14 12:20:46', '2025-07-14 12:20:46'),
(27, 2, 'Classic Gold-Plated Hoop Earrings', 'classic-gold-plated-hoop-earrings', 'EGR-CLSHOOP-6633', '<p>&nbsp;</p><p>Elevate your everyday look with these classic gold-plated hoop earrings. Featuring a sleek, bold silhouette and a radiant finish, these hoops are a wardrobe essential for every jewellery lover. Lightweight yet sturdy, they offer all-day comfort with a secure clasp lock. Ideal for both casual and formal occasions, these earrings blend effortlessly with any outfit.</p>', 'Timeless gold-plated hoop earrings crafted for everyday sophistication.', 399.00, 189.00, 10, 1, 1, 0, 'active', '[\"products\\/1ANmC10rIYQHiS30B7lwc1GdmcAputM41WHLjR2k.jpg\"]', '{\"style\":\"Hoop\",\"design\":\"Rounded Classic\",\"finish\":\"Glossy Gold\",\"metal_base\":\"Brass Alloy\",\"plating\":\"1 Micron Gold\",\"ideal_for\":\"Women\",\"occasion\":\"Formal,Casual,Daily Wear\",\"closure\":\"Clasp Lock\",\"origin\":\"India\",\"handmade\":\"false\",\"weight\":\"7 grams\"}', NULL, 15.00, NULL, NULL, NULL, NULL, 'Na', 0, '2025-07-14 12:26:33', '2025-07-14 12:26:33'),
(28, 2, 'Bold Glossy Hoop Earrings', 'bold-glossy-hoop-earrings', 'EGR-BGH-6633', '<p>These bold, glossy hoop earrings are the ultimate statement piece for fashion lovers. Designed with a thicker band and radiant high-polish finish, they add effortless glam to both western and ethnic styles. Lightweight yet impactful, they are secured with a firm clasp for comfortable wear. Ideal for parties, brunches, or everyday styling, these hoops are your new go-to accessory.</p>', 'Statement hoop earrings with a bold, glossy gold finish – perfect for day-to-night looks.', 399.00, 185.00, 10, 1, 1, 0, 'active', '[\"products\\/jvcxH6fvBqRTJ4ky2mr9H1StudguxYe4DD8UzrLb.jpg\"]', '{\"style\":\"Hoop Earrings\",\"design\":\"Bold Glossy\",\"closure\":\"Clasp Lock\",\"finish\":\"Mirror Shine\",\"metal_base\":\"Brass Alloy\",\"plating\":\"1 Micron Gold\",\"ideal_for\":\"Women\",\"occasion\":\"Casual,Party Wear,Daily Use\",\"origin\":\"India\",\"handmade\":\"false\",\"weight\":\"8.5 grams\"}', NULL, 15.00, NULL, NULL, NULL, NULL, 'Na', 0, '2025-07-14 12:32:57', '2025-07-14 12:32:57'),
(29, 9, 'Oxidised Elephant Motif Earrings', 'oxidised-elephant-motif-earrings', 'OX-ELPH-7310', '<p>Celebrate heritage and culture with these handcrafted oxidised earrings featuring majestic elephant motifs and delicate pearl danglers. The top disc is embossed with traditional Indian patterns and adorned with sparkling white stones. The elephants symbolize wisdom and strength, making these earrings not only a fashion statement but also a symbol of good fortune. Lightweight yet durable, these earrings are ideal for festive occasions, cultural events, or styling up ethnic wear.</p>', 'Traditional oxidised earrings featuring intricate elephant motifs and pearl danglers.', 599.00, 305.00, 18, 1, 1, 0, 'active', '[\"products\\/5mOQOozduoazuSE4lzrO0AHkk8o9VQRFGxkxJrHa.jpg\"]', '{\"style\":\"Oxidised Ethnic\",\"design\":\"Elephant Motif with Pearl Danglers\",\"closure\":\"Push Back\",\"finish\":\"Matte Silver Oxidised\",\"metal_base\":\"Brass Alloy\",\"stone_details\":\"Faux Pearls,Synthetic White Stones\",\"ideal_for\":\"Women,Girls\",\"occasion\":\"Traditional,Festive,Cultural Events\",\"origin\":\"India\",\"handmade\":\"true\",\"weight\":\"18 grams\"}', NULL, NULL, NULL, NULL, 'Synthetic White Stones & Faux Pearls', NULL, 'Na', 0, '2025-07-15 23:08:38', '2025-07-15 23:08:38'),
(30, 9, 'Black Stone Tribal Oxidised Earrings', 'black-stone-tribal-oxidised-earrings', 'OX-BLK-7303', '<p>&nbsp;</p><p>📜 <strong>Full Description&nbsp;</strong></p><p>These statement oxidised earrings are a perfect fusion of tradition and bold elegance. Crafted with a unique tribal-inspired design, they feature prominent black oval and round stones encased in ornate silver frames. The pointed arches with textured beading add a dramatic flair, while the intricate metalwork offers a rich handcrafted feel. These earrings are ideal for those who love distinctive, statement-making jewellery to complement ethnic or Indo-western outfits</p>', 'Bold oxidised earrings with black stones and intricate tribal design.', 499.00, 265.00, 10, 1, 1, 0, 'active', '[\"products\\/myThpDcSY3KsCXSqKIEZLG0UGhGFXqDTxKK3yR4Z.jpg\"]', '{\"style\":\"Ethnic\\/Tribal\",\"design\":\"Bold Black Stone with Triangle Detailing\",\"closure\":\"Push Back\",\"finish\":\"Oxidised Silver\",\"metal_base\":\"Brass Alloy\",\"stone_details\":\"Synthetic Black Stones\",\"ideal_for\":\"Women,Girls\",\"occasion\":\"Festive,Ethnic,Fusion Wear\",\"origin\":\"India\",\"handmade\":\"true\",\"weight\":\"21 grams\"}', '[\"NA\"]', 20.00, NULL, NULL, 'Synthetic Black Stone', 8.00, 'Na', 0, '2025-07-15 23:13:54', '2025-07-15 23:24:33'),
(31, 9, 'Oxidised Floral Circle Drop Earrings', 'oxidised-floral-circle-drop-earrings', 'OX-FLR-7299', '<p>These oxidised earrings blend boho-chic vibes with traditional craftsmanship. The circular drop design is accented with delicate silver balls hanging from dual rods, while the top part boasts a vibrant hand-painted floral enamel in shades of pink, orange, and white. The antique silver finish adds a rustic charm, making them perfect for both casual and ethnic occasions. Lightweight yet bold, these earrings make a versatile and elegant addition to any accessory collection.</p>', 'Stylish oxidised earrings featuring enamel floral studs and unique hanging circle drops.', 599.00, 289.00, 10, 1, 1, 0, 'active', '[\"products\\/hp5I5xD0GCU8pYk3SzD7wn77DfdN9XhERnxgE0LN.jpg\"]', '{\"style\":\"Boho Ethnic\",\"design\":\"Circle Drop with Enamel Floral Stud\",\"closure\":\"Push Back\",\"finish\":\"Oxidised Antique\",\"metal_base\":\"Brass Alloy\",\"stone_details\":\"Hand-painted enamel,Synthetic silver beads\",\"ideal_for\":\"Women,Girls\",\"occasion\":\"Festive,Casual,Ethnic\",\"origin\":\"India\",\"handmade\":\"true\",\"weight\":\"18 grams\"}', '[\"Free size\"]', 18.00, NULL, NULL, 'Enamel work with synthetic beads', 5.00, 'Na', 0, '2025-07-15 23:21:52', '2025-07-15 23:21:52'),
(32, 9, 'Black & White Teardrop Statement Earrings', 'black-white-teardrop-statement-earrings', 'JW-BW-TD-001', '<p>&nbsp;</p><p><strong>Full Description</strong></p><p>Make a powerful style statement with these intricately designed black-and-white teardrop earrings. Featuring a striking mix of black onyx-style stones and clear crystal-look accents, these earrings offer a classic yet bold aesthetic. The design resembles a stylized floral or paw motif, with high-polish detailing and a secure backing. Ideal for festive, formal, or ethnic occasions.</p>', 'Bold and elegant black-and-white teardrop earrings, crafted for a statement look.', 599.00, 249.00, 10, 1, 1, 0, 'active', '[\"products\\/BCsQ1TWy5Pry5H7MOrpc3b9rHYKAdEf4TsjCNDWr.jpg\"]', '{\"closure\":\"Push-back\",\"style\":\"Statement\",\"theme\":\"Traditional, Festive\",\"occasion\":\"Wedding, Party, Festival\",\"plating\":\"Silver-tone\",\"stone_shape\":\"Teardrop\"}', NULL, 19.00, NULL, NULL, 'Black Onyx Imitation & Clear Crystal Rhinestones', 10.00, 'Na', 0, '2025-07-15 23:44:38', '2025-07-15 23:44:38'),
(33, 9, 'Emerald and Ruby Peacock Earrings', 'emerald-and-ruby-peacock-earrings', '*SKU:* JWL-001', '<p>*Short Description:* Elegant peacock-shaped earrings featuring emerald and ruby stones, perfect for adding a touch of traditional Indian style to any outfit.</p><p>*Full Description:*</p><p>These stunning peacock earrings are crafted with intricate detail and feature a vibrant combination of emerald and ruby stones. The peacock design is a classic symbol of beauty and grace in Indian culture, making these earrings a perfect accessory for any occasion. The gold-toned metalwork adds a luxurious touch, while the teardrop-shaped stones provide a sophisticated finish.</p>', '*Short Description:* Elegant peacock-shaped earrings featuring emerald and ruby stones, perfect for adding a touch of traditional Indian style to any outfit.', 599.00, 249.00, 10, 1, 1, 0, 'active', '[\"products\\/WIEbLUdNVUt2zFHNrBOW0HBYWDAJ3bnfsIFEh0tJ.jpg\"]', '{\"Material\":\"Gold-toned metal\",\"Stone Color\":\"Emerald green, Ruby red\"}', '[\"Free size\"]', 19.00, NULL, NULL, 'Emerald, Ruby', 10.00, 'Na', 0, '2025-07-16 00:06:26', '2025-07-16 01:44:40'),
(34, 9, 'Red and White Stone Earrings', 'red-and-white-stone-earrings', 'RDSE001', '<p>&nbsp;A pair of silver-toned earrings featuring red and white stones in a teardrop design.</p><p>*Full Description:* These elegant earrings are crafted with silver-toned metal and adorned with red and white stones in a teardrop shape. The stones are arranged in a flower-like pattern, adding a touch of sophistication to any outfit. Perfect for formal or casual wear, these earrings are sure to make a statement.</p>', 'A pair of silver-toned earrings featuring red and white stones in a teardrop design.', 599.00, 249.00, 10, 1, 1, 0, 'active', '[\"products\\/pjNuQ3gNMyqbDYDbw6xueBqZ9NIH4DiIKQbYp6WD.jpg\"]', '{\"metal_finish\":\"Polished\",\"stone_clarity\":\"VS1\",\"stone_cut\":\"Teardrop\"}', '[\"NA\"]', 19.00, NULL, NULL, 'Red stone', 10.00, 'Na', 0, '2025-07-16 00:28:00', '2025-07-16 01:44:20'),
(35, 9, 'Traditional Indian Jhumka Earrings with Pearl Details', 'traditional-indian-jhumka-earrings-with-pearl-details', 'IJ001', '<p>&nbsp;</p><p>*Full Description:*</p><p>These traditional Indian jhumka earrings are a perfect blend of elegance and cultural heritage. The earrings feature a silver-toned metal with intricate designs and patterns, complemented by a blue stone at the top. The lower part of the earring is adorned with small pearls, adding a touch of sophistication. The jhumka design is classic and timeless, making it suitable for various occasions.</p>', 'Elegant jhumka earrings featuring intricate designs, pearl accents, and a touch of blue stone', 499.00, 239.00, 10, 1, 1, 0, 'active', '[\"products\\/2b65qC9gYe6ni58gbwLd3CukrqlXgEQW8fms3TSz.jpg\"]', '{\"description\":\"*Additional Specifications:* {\\\"Material\\\": \\\"Silver-toned\\\", \\\"Finish\\\": \\\"Polished\\\", \\\"Closure\\\": \\\"Hook\\\"}\"}', '[\"Free size\"]', 15.00, NULL, NULL, 'Blue onyx', 5.00, 'NA', 0, '2025-07-16 00:32:48', '2025-07-16 00:32:48'),
(36, 9, 'Red Stone peacock, Jhumka Earrings', 'red-stone-peacock-jhumka-earrings', 'RJ001', '<p>&nbsp;</p><p>*Full Description:*<br>These stunning jhumka earrings are a perfect blend of traditional Indian craftsmanship and modern style. The earrings feature intricate silver metalwork adorned with vibrant red stones and delicate pearl details. The jhumka design adds a touch of elegance and sophistication, making them ideal for special occasions or everyday wear.</p>', 'Traditional Indian jhumka earrings with red stones and pearl details.', 599.00, 289.00, 12, 1, 1, 0, 'active', '[\"products\\/ioLSH7aCVIrKotpJqQGWK803L2o3uqjbk0QTh55q.jpg\"]', '{\"Material\":\"Silver\",\"Stone Color\":\"Red\",\"Pearl Color\":\"White\"}', '[\"Na\"]', 15.00, NULL, NULL, 'Red Stone (imitation ruby)', 7.00, 'Na', 0, '2025-07-16 00:38:43', '2025-07-16 00:39:00'),
(37, 5, 'Kundan Necklace Set', 'kundan-necklace-set', 'KND001', '<p>&nbsp;</p><p>*Full Description:*<br>This stunning Kundan necklace set is a perfect blend of traditional craftsmanship and modern elegance. The set includes a necklace and earrings, both adorned with pink and white stones that add a touch of sophistication to any outfit. The necklace features seven pendants, each with a unique design, while the earrings boast a similar aesthetic. Made from high-quality metal, this set is durable and long-lasting.</p>', 'Elegant Kundan necklace set featuring intricate designs with pink and white stones.', 1999.00, 982.00, 10, 1, 1, 0, 'active', '[\"products\\/dnHoElikYAgFm1DjhpWq2jiPiNSat97XZap0VaYj.jpg\"]', '{\"description\":\"*Additional Specifications:* {\\\"Material\\\": \\\"Gold-plated\\\", \\\"Color\\\": \\\"Pink and White\\\", \\\"Occasion\\\": \\\"Wedding, Party, Formal\\\"}\"}', NULL, 20.00, NULL, NULL, 'Kundan', 10.00, 'NA', 0, '2025-07-16 03:07:16', '2025-07-16 03:07:16'),
(38, 3, 'Golden Palm Leaf Earrings with Mother of Pearl', 'golden-palm-leaf-earrings-with-mother-of-pearl', 'GP001', '<p>&nbsp;</p><p>These stunning golden palm leaf earrings are adorned with lustrous mother of pearl stones, creating a sophisticated and eye-catching accessory. The intricate design of the palm leaves adds a touch of elegance, while the mother of pearl stones provide a subtle shimmer. Perfect for formal events or everyday wear, these earrings are sure to make a statement.</p>', 'Elegant golden palm leaf earrings featuring mother of pearl stones, perfect for adding a touch of nature-inspired beauty to any outfit', 599.00, 249.00, 10, 1, 1, 0, 'active', '[\"products\\/Bt2HKLvqttzyPdBmARgn34guInnduGrxIAQiWme4.jpg\"]', '{\"Material\":\"Gold Plated\",\"Closure\":\"Post Back\"}', '[\"NA\"]', 10.00, NULL, NULL, 'Mother of Pearl', 5.00, 'Na', 0, '2025-07-16 12:09:11', '2025-07-16 12:09:11'),
(39, 3, 'Golden Deer Earrings', 'golden-deer-earrings', 'GD001', '<p>&nbsp;</p><p>*Full Description:*</p><p>These stunning golden earrings are designed to capture the essence of nature. Featuring intricate deer and tree designs within a circular frame, these earrings are perfect for those who appreciate wildlife-inspired jewelry. The use of high-quality gold and precious stones ensures durability and elegance. Ideal for formal events or as a special gift.</p>', 'Elegant golden earrings featuring deer and tree designs, perfect for adding a touch of nature to your outfit.', 599.00, 390.00, 10, 1, 1, 0, 'active', '[\"products\\/CGIKZ9Cp941BRxV2wKISOvkDPOEREfcHaMEyI6KB.jpg\"]', '{\"Material\":\"Gold\",\"Color\":\"Golden\",\"Style\":\"Nature-inspired\"}', '[\"Free size\"]', 15.00, NULL, NULL, 'Black Onyx, Mother of Pearl', 8.00, 'Na', 0, '2025-07-16 12:16:57', '2025-07-16 12:16:57'),
(40, 3, 'Traditional Indian Gold-Toned Earrings with Red Enamel and Dangling Bells', 'traditional-indian-gold-toned-earrings-with-red-enamel-and-dangling-bells', 'TJ001', '<p>&nbsp;</p><p>*Full Description:*</p><p>These stunning earrings are a perfect blend of traditional Indian craftsmanship and modern elegance. The gold-toned metalwork features intricate designs and patterns, while the red enamel accents add a pop of color. The dangling bells create a beautiful sound and add movement to the earrings. The earrings are lightweight and comfortable to wear, making them perfect for everyday wear or special occasions.<br>&nbsp;</p>', 'Elegant gold-toned earrings featuring intricate designs, red enamel accents, and dangling bells, perfect for traditional Indian attire.', 899.00, 704.00, 15, 1, 1, 0, 'active', '[\"products\\/vxHtoiQxHZLgrGEWbYMLumGevOiPldHgA3yBDMng.jpg\"]', '{\"Material\":\"Gold-Toned Metal\",\"Color\":\"Gold and Red\",\"Style\":\"Traditional Indian\"}', '[\"Free sizes for\"]', 15.00, NULL, NULL, NULL, NULL, 'Na', 0, '2025-07-16 12:21:38', '2025-07-16 12:21:38'),
(41, 3, 'Pink and Silver Kundan Jhumka Earrings', 'pink-and-silver-kundan-jhumka-earrings', 'KJ001', '<p>&nbsp;</p><p>*Full Description:*</p><p>These stunning Kundan Jhumka earrings are a perfect blend of traditional Indian craftsmanship and modern style. The earrings feature a pink square top with a silver and gold Kundan design, attached to a silver half-moon shaped base with an intricate peacock design. The base is adorned with small gold bells and flowers, adding a touch of elegance and sophistication. The earrings are made of high-quality metal and stone, ensuring durability and long-lasting shine.</p>', 'Traditional Indian Kundan Jhumka earrings with pink and silver design, adorned with small bells and flowers.', 899.00, 704.00, 14, 1, 1, 0, 'active', '[\"products\\/j4GvYNIStcUMh7zpZj6uxrMugckVUEoBP2x7iXfi.jpg\"]', '{\"material\":\"Silver\",\"stone_color\":\"Pink and White\",\"design\":\"Kundan Jhumka\",\"occasion\":\"Wedding, Festival, Party\"}', '[\"Free size\"]', 20.00, NULL, NULL, 'Kundan', 5.00, 'Na', 0, '2025-07-16 12:27:03', '2025-08-24 08:21:29'),
(42, 2, 'Minimalist Abstract Face Pendant Necklace', 'minimalist-abstract-face-pendant-necklace', 'NCKL-FACE-2025', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>This stainless steel necklace features a dainty gold-toned chain with beaded accents and a uniquely crafted abstract face pendant. Inspired by modern art, the design adds a contemporary yet elegant touch to any outfit. It is lightweight, skin-friendly, and ideal for gifting or personal use. Tarnish-resistant and designed to last with daily wear.</p>', 'Gold-toned minimalist necklace featuring a unique abstract face pendant, perfect for modern everyday wear.', 599.00, 249.00, 15, 1, 1, 0, 'active', '[\"products\\/vG6SCyqNXP5hpQPnSo7qgswMQrbtJifkg9VfX2H0.jpg\"]', '{\"PendantShape\":\"Abstract Face\",\"ChainType\":\"Beaded Cable Chain\",\"ClaspType\":\"Lobster Claw\",\"TarnishFree\":\"true\",\"Occasion\":\"Casual,Gifting,Minimalist Wear\"}', NULL, 8.00, NULL, NULL, NULL, NULL, 'Na', 0, '2025-07-19 00:45:57', '2025-07-19 00:45:57'),
(43, 9, 'Antique Ruby Red Jhumka Earrings', 'antique-ruby-red-jhumka-earrings', 'JHUMKA-RB-2025', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>Elevate your ethnic ensemble with these beautifully crafted oxidised jhumka earrings. Designed with a blend of traditional charm and bold colors, these earrings feature striking ruby-red stones, intricate floral motifs, and delicately carved dome-shaped jhumkas adorned with white faux pearls. The oxidised finish enhances the vintage appeal, making them ideal for festive wear, weddings, or gifting. Lightweight and skin-friendly, these jhumkas bring timeless elegance to your jewellery collection.</p>', 'Oxidised silver-tone jhumka earrings with ruby red stones, floral motifs, and pearl detailing – a timeless ethnic statement piece.', 499.00, 290.00, 10, 1, 1, 0, 'active', '[\"products\\/9poknB4IHimUJTxB0rSkbXotZMWocGLZUgq1Cov5.jpg\"]', '{\"EarringType\":\"Jhumka\",\"Finish\":\"Oxidised Silver\",\"PrimaryStone\":\"Synthetic Ruby\",\"SecondaryStone\":\"Faux Pearls\",\"ClosureType\":\"Push Back\",\"Occasion\":\"Traditional,Festive,Wedding\",\"Style\":\"Ethnic\"}', NULL, 15.00, NULL, NULL, 'Synthetic Ruby, Faux Pearls', 7.00, 'NA', 0, '2025-07-19 00:51:38', '2025-07-19 00:51:38'),
(44, 9, 'Antique Mustard Yellow Jhumka Earrings', 'antique-mustard-yellow-jhumka-earrings', 'JHUMKA-YLW-2025', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>Brighten your traditional look with these mustard yellow jhumka earrings, featuring oxidised silver-tone metalwork, floral motifs, and dome-shaped jhumkas adorned with dangling white faux pearls. The bold yellow stones bring a fresh pop of color, while the intricate detailing and vintage finish reflect timeless Indian craftsmanship. Ideal for festive occasions, ethnic wear, or statement styling, these earrings are both lightweight and skin-friendly for all-day wear.</p>', 'Oxidised silver jhumkas with mustard yellow stones and pearl drops, blending vibrant charm with traditional elegance.', 499.00, 290.00, 10, 1, 1, 0, 'active', '[\"products\\/2hWycZX3uGswoX7Mhs02mtkyuokyJjapOAVEOPA3.jpg\"]', '{\"EarringType\":\"Jhumka\",\"Finish\":\"Oxidised Silver\",\"PrimaryStone\":\"Synthetic Yellow Stone\",\"SecondaryStone\":\"Faux Pearls\",\"ClosureType\":\"Push Back\",\"Occasion\":\"Traditional,Festive,Casual\",\"Style\":\"Ethnic\"}', NULL, 15.00, NULL, NULL, 'Synthetic Yellow Stone, Faux Pearls', 7.00, NULL, 0, '2025-07-19 00:57:28', '2025-07-19 00:57:28'),
(45, 2, 'Minimalist Hollow Heart Pendant Necklace', 'minimalist-hollow-heart-pendant-necklace', 'NCKL-HRT-2025', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>This minimalist hollow heart pendant necklace is crafted in a sleek gold finish that radiates understated elegance. Designed with a smooth snake chain and an open heart centerpiece, this piece symbolizes love and grace. Whether it’s a romantic gift or a daily-wear essential, this necklace offers timeless appeal. Made from durable stainless steel with a tarnish-free finish, it’s both stylish and skin-friendly, making it ideal for all-day comfort and wear.</p>', 'Elegant gold-toned necklace featuring a hollow heart pendant – a perfect blend of simplicity and charm for everyday wear or gifting.', 499.00, 240.00, 15, 1, 1, 0, 'active', '[\"products\\/8B5zNWxFh1OUwCexGQjKdU8MY4T6HwvX63s0hEC4.jpg\"]', '{\"PendantShape\":\"Hollow Heart\",\"ChainType\":\"Snake Chain\",\"Finish\":\"18K Gold Plated\",\"ClosureType\":\"Lobster Clasp\",\"TarnishFree\":\"true\",\"Occasion\":\"Casual,Romantic Gift,Minimalist Wear\",\"Style\":\"Modern\"}', NULL, 7.00, NULL, NULL, NULL, NULL, NULL, 0, '2025-07-19 01:03:59', '2025-07-19 01:03:59'),
(46, 2, 'Emerald Green Baguette Pendant Necklace', 'emerald-green-baguette-pendant-necklace', 'NCKL-EMG-2025', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>Add a pop of luxurious green to your jewellery collection with this emerald-green baguette pendant necklace. Featuring a sleek rectangular stone set in a minimalist gold bezel and suspended on a smooth gold-toned snake chain, this piece is ideal for both modern and traditional outfits. Crafted from durable stainless steel with a high-quality gold plating, it offers a lightweight feel and a rich finish that resists tarnishing. Perfect for gifting, layering, or making a solo statement.</p>', 'Elegant gold-plated necklace with a vertical emerald-green baguette pendant — perfect for adding a touch of sophistication to your everyday or occasion look.', 699.00, 299.00, 15, 1, 1, 0, 'active', '[\"products\\/Et5DZOdOjZcbv8c86t2S9COX43BEgnp3Vncxdfn4.jpg\"]', '{\"PendantShape\":\"Baguette Rectangle\",\"StoneColor\":\"Emerald Green\",\"ChainType\":\"Snake Chain\",\"Finish\":\"18K Gold Plated\",\"ClosureType\":\"Lobster Clasp\",\"TarnishFree\":\"true\",\"Occasion\":\"Party,Everyday Wear,Festive\",\"Style\":\"Modern, Elegant\"}', NULL, 12.00, NULL, NULL, 'Synthetic Emerald', 4.00, NULL, 0, '2025-07-19 01:16:27', '2025-07-19 01:16:27');
INSERT INTO `products` (`id`, `category_id`, `name`, `slug`, `sku`, `description`, `short_description`, `price`, `sale_price`, `stock_quantity`, `manage_stock`, `in_stock`, `is_featured`, `status`, `images`, `specifications`, `sizes`, `weight`, `metal_type`, `metal_purity`, `stone_type`, `stone_weight`, `certification`, `sort_order`, `created_at`, `updated_at`) VALUES
(47, 2, 'Dainty Mini Baguette Drop Necklace', 'dainty-mini-baguette-drop-necklace', 'SKU: NCKL-MBG-2025', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>Elevate your everyday look with this dainty baguette drop necklace. Crafted with a delicate chain and adorned with multiple mini rectangular baguette charms, it brings a soft shimmer and elegant charm to any outfit. The gold-plated finish ensures lasting shine, while the lightweight design makes it comfortable for all-day wear. Whether worn solo or layered, this necklace offers a modern yet timeless accessory choice for minimal jewellery lovers.</p>', 'Minimalist gold-plated chain necklace featuring multiple small baguette drops — a subtle sparkle for everyday chic.', 599.00, 295.00, 15, 1, 1, 0, 'active', '[\"products\\/uvKICXTU9ZCNHaMiZ1lkggAp2HrIJxfRdSlbDVqR.jpg\"]', '{\"PendantStyle\":\"Mini Baguette Charms\",\"StoneColor\":\"Clear White\",\"ChainType\":\"Cable Chain\",\"Finish\":\"18K Gold Plated\",\"ClosureType\":\"Lobster Clasp\",\"TarnishFree\":\"true\",\"Occasion\":\"Casual,Office,Brunch,Gifting\",\"Style\":\"Minimalist, Dainty\"}', NULL, 10.00, NULL, NULL, '4', NULL, NULL, 0, '2025-07-19 01:20:54', '2025-07-19 01:20:54'),
(48, 3, 'Royal Ruby Drop Pearl Earrings', 'royal-ruby-drop-pearl-earrings', 'ER-RRDP-3045', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>Make a royal statement with our Ruby Drop Pearl Earrings, featuring a bold teardrop-shaped red gemstone surrounded by a halo of shimmering CZ stones. These earrings are finished with lustrous pearl drops that add a touch of grace and femininity. Ideal for festive occasions, weddings, or a glamorous evening out, they’re lightweight and designed to be comfortably worn for hours. The gold-plated setting adds warmth and richness to the overall aesthetic, making them a standout piece in your jewellery collection.</p>', 'A stunning pair of ruby-colored stud earrings with pearl drops — the perfect blend of regal elegance and modern charm.', 399.00, 305.00, 10, 1, 1, 0, 'active', '[\"products\\/L9ubQp3tHG1PclJfLDIVJCEgUtG3r1d0r8czUPVK.jpg\"]', '{\"MainStoneShape\":\"Teardrop\",\"AccentStone\":\"Cubic Zirconia\",\"Drop\":\"Pearl Bead\",\"Finish\":\"Glossy Gold\",\"Fastening\":\"Push Back\",\"TarnishFree\":\"true\",\"Style\":\"Traditional, Festive\",\"Occasion\":\"Weddings,Festivals,Party Wear\"}', NULL, 10.00, NULL, NULL, 'Synthetic Ruby, CZ, Faux Pearl', 5.00, NULL, 0, '2025-07-19 01:27:24', '2025-07-19 01:27:24'),
(49, 3, 'Crown Dome Ruby Drop Earrings', 'crown-dome-ruby-drop-earrings', 'ER-CDRD-3762', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>Inspired by royal crown motifs, these Crown Dome Ruby Drop Earrings feature an exquisite blend of tradition and glamour. The upper dome section is intricately studded with dazzling CZ stones and colored accents, resembling a mini tiara, while the bottom showcases a glossy ruby-red round stone framed in a sparkling halo. The teardrop-shaped stud top adds to the richness and stability of the design. Perfect for festive wear, bridal looks, and ethnic outfits, these earrings are lightweight yet luxurious and designed to turn heads.</p>', 'Elegant dome-shaped earrings crowned with shimmering stones and a bold red centerpiece, perfect for grand occasions.', 399.00, 305.00, 10, 1, 1, 0, 'active', '[\"products\\/3pleGSfGfjQZmn0aTtpNV1pqNV6FOsd9jnwLRYA4.jpg\"]', '{\"TopShape\":\"Teardrop CZ Stud\",\"MainDesign\":\"Crown-Dome Drop\",\"AccentStones\":\"Cubic Zirconia\",\"CenterStone\":\"Synthetic Ruby\",\"ColorAccents\":\"Green,Red,Black CZ\",\"Finish\":\"High Gloss Gold\",\"Fastening\":\"Push Back\",\"Style\":\"Bridal, Festive, Royal\",\"Occasion\":\"Weddings,Festivals,Traditional Events\"}', NULL, 10.00, NULL, NULL, 'Synthetic Ruby, Multi CZ Stones', 5.00, NULL, 0, '2025-07-19 01:34:18', '2025-07-19 01:34:18'),
(50, 9, 'Tribal Crescent Oxidised Earrings', 'tribal-crescent-oxidised-earrings', 'ER-TCOE-4217', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>Add a rustic charm to your look with these Tribal Crescent Oxidised Earrings. Designed in a unique crescent moon shape, the earrings feature an antique silver finish with intricate granule-like beaded edges, evoking traditional tribal aesthetics. Crafted from durable metal alloy and finished with a matte oxidised coating, these earrings are a striking choice for ethnic wear, festive occasions, and Indo-western outfits. Lightweight yet statement-making, they offer the perfect blend of heritage and contemporary fashion.</p>', 'Bold oxidised crescent-shaped earrings with traditional beaded detailing — a timeless piece for ethnic looks.', 299.00, 195.00, 12, 1, 1, 0, 'active', '[\"products\\/HSnyzl9TmZjoxnbojAWzEfaVWHRVrstx9bVLWSOr.jpg\"]', '{\"Design\":\"Crescent Moon\",\"Style\":\"Oxidised \\/ Tribal\",\"Finish\":\"Antique Matte Silver\",\"Fastening\":\"Post with Push Back\",\"Accents\":\"Beaded Ball Border\",\"Stone\":\"None\",\"Occasion\":\"Festivals,Traditional,Indo-western\",\"TargetAudience\":\"Women, Girls\"}', NULL, 10.00, NULL, NULL, NULL, NULL, NULL, 0, '2025-07-19 01:38:03', '2025-07-19 01:38:03'),
(51, 9, 'राम\" Floral Enamel Statement Earrings', 'rama-floral-enamel-statement-earrings', 'SKU: ER-RAMFLR-5631', '<p>&nbsp;</p><p><strong>Full Description</strong>:<br>Celebrate culture, faith, and fashion with these unique “राम” Floral Enamel Statement Earrings. Designed in an oxidised silver-tone, the earrings feature the word \"राम\" (Ram) in Devanagari script above a circular floral enamel centerpiece in vibrant colors. Accented with delicate white pearl beads hanging from the bottom, they blend spirituality with traditional craftsmanship. Lightweight and versatile, these earrings are ideal for festive occasions, temple visits, and everyday ethnic outfits that need a hint of sacred elegance.</p>', 'Oxidised \"राम\" earrings with hand-painted floral enamel art and pearl bead danglers — perfect for festive and spiritual styling.', 299.00, 225.00, 10, 1, 1, 0, 'active', '[\"products\\/PHYzU7iGFFjDPCB4M6SiIf6N8sVlchEkiRthsviy.png\"]', '{\"Design\":\"Devotional\",\"Text\":\"\\u0930\\u093e\\u092e\",\"ArtStyle\":\"Hand-painted Enamel Floral\",\"Finish\":\"Oxidised Silver\",\"Accents\":\"Dangling Faux Pearls\",\"Fastening\":\"Post with Push Back\",\"Occasion\":\"Festive,Cultural Events,Temple Visits\",\"TargetAudience\":\"Women, Girls\"}', NULL, 12.00, NULL, NULL, NULL, NULL, NULL, 0, '2025-07-19 01:43:00', '2025-07-19 01:43:00'),
(52, 9, 'Blue Leaf Earrings', 'blue-leaf-earrings', 'E.G.ble001', '<p><br>These stunning earrings are crafted with intricate detail, featuring a leaf-shaped upper portion made from silver. The lower part showcases a beautiful blue stone surrounded by a delicate arrangement of pearls, adding a touch of elegance and sophistication to any outfit.</p>', 'Elegant silver earrings featuring a leaf design with a blue stone and pearl embellishments.', 399.00, 295.00, 10, 1, 1, 0, 'active', '[\"products\\/Njt3Kc5sZ3duk6qF93XLRoW7FvMoht9VsxKddQ7M.jpg\"]', '{\"Material\":\"Silver\",\"StoneColor\":\"Blue\",\"PearlType\":\"Freshwater\"}', NULL, 19.00, NULL, NULL, NULL, NULL, NULL, 0, '2025-07-19 01:49:07', '2025-07-19 01:49:07'),
(53, 9, 'Black Stone Leaf Earrings', 'black-stone-leaf-earrings', 'BSLE001', '<p>These earrings are designed with a leaf motif at the top and a large black stone at the center, surrounded by a halo of pearls. The combination of the leaf design and the black stone with pearls gives these earrings a unique and elegant look, perfect for formal occasions.</p>', 'Elegant earrings featuring a leaf design with a black stone and pearls.', 499.00, 295.00, 14, 1, 1, 0, 'active', '[\"products\\/CioSgNke4xHnvOOr1Shgz4UuUrVClJA17EcKTL73.jpg\"]', '{\"Material\":\"Silver-toned\",\"StoneColor\":\"Black\",\"PearlColor\":\"White\"}', NULL, 15.00, NULL, NULL, NULL, NULL, NULL, 0, '2025-07-19 01:55:05', '2025-08-23 12:25:08'),
(54, 3, 'Traditional Gold Earrings with Black and white stone', 'traditional-gold-earrings-with-black-and-white-stone', 'Tg001', '<p>These traditional gold earrings are designed with intricate details, featuring black and white stones at their center. The lower part of the earrings is adorned with gold flowers and beads, giving them a classic and elegant look. Perfect for special occasions and traditional wear.</p>', 'Elegant gold earrings featuring black and white stones, adorned with gold flowers and beads.', 999.00, 875.00, 10, 1, 1, 0, 'active', '[\"products\\/QH6Y2oNSz3FjdgJy3eb08mstY3QyGBsO2gn4Bfd4.jpg\"]', '{\"occasion\":\"Traditional, Wedding\",\"style\":\"Classic, Elegant\"}', NULL, 20.00, NULL, NULL, 'White Diamonds, Black Onyx, Emerald', 8.00, NULL, 0, '2025-07-19 02:10:31', '2025-07-19 02:10:31'),
(55, 7, 'Pearl Accent Gold Chain with Pendant', 'pearl-accent-gold-chain-with-pendant', 'GC001', '<p><br>Full Description: This exquisite gold chain features small white pearls and a beautifully crafted pendant with a gemstone and a pearl. The chain is made of high-quality gold and is perfect for formal occasions.</p>', 'Elegant gold chain with pearl accents and a matching pendant.', 599.00, 280.00, 10, 1, 1, 0, 'active', '[\"products\\/ZN3gilcQ8Aqy10Jr4o3NJt2JFbLz4CMow7kuDaMM.jpg\"]', '{\"chain_length\":\"18 inches\",\"pendant_size\":\"2 cm\"}', NULL, 15.00, NULL, NULL, 'Diamond', 2.00, NULL, 0, '2025-07-19 02:23:06', '2025-07-19 02:23:06'),
(56, 3, 'Traditional Indian Jhumka Earrings', 'traditional-indian-jhumka-earrings', 'Tj0010', '<p>&nbsp;</p><p>*Full Description:* These stunning jhumka earrings are a perfect blend of traditional craftsmanship and modern elegance. The teardrop-shaped top is adorned with a pink stone, a green stone, and a white pearl, all surrounded by a gold rope design. The silver-colored jhumka is intricately designed with patterns and embellished with white pearls, creating a beautiful and eye-catching effect.</p>', 'Exquisite traditional Indian jhumka earrings featuring intricate designs and pearl embellishments.', 999.00, 852.00, 10, 1, 1, 0, 'active', '[\"products\\/1CgZhKWSiG5JkN5SCgYoItdbbYHJtpbVYrGvzwBg.jpg\"]', '{\"description\":\"{\\r\\n  \\\"occasion\\\": \\\"Wedding, Festival, Party\\\",\"}', NULL, 20.00, NULL, NULL, 'Pink stone', 0.50, NULL, 0, '2025-07-19 02:34:09', '2025-07-19 02:34:09'),
(57, 9, 'Silver Dove Brooch Pair', 'silver-dove-brooch-pair', 'SD_BP_001', '<p>This exquisite pair of silver dove brooches is crafted with precision and care. The doves are depicted in flight, holding olive branches in their beaks, symbolizing peace and harmony. Made from high-quality silver, these brooches are perfect for formal occasions or as a meaningful gift.</p>', 'Elegant silver dove brooch pair, symbolizing peace and love.', 299.00, 210.00, 10, 1, 1, 0, 'active', '[\"products\\/u1hihz8NK7UqIrC91VM6RGgaDKuqO0YiOjJVJwt2.jpg\"]', '{\"Dimensions\":\"Length: 3 cm, Width: 2 cm\",\"Packaging\":\"Comes in a velvet pouch\",\"Warranty\":\"1 year against manufacturing defects\"}', NULL, 10.00, NULL, NULL, NULL, NULL, NULL, 0, '2025-07-19 02:40:50', '2025-07-19 02:40:50'),
(58, 9, '\"Ruby Peacock Earrings.\"', 'ruby-peacock-earrings', 'JE001', '<p>&nbsp;\"<br>- Full Description: \"These stunning earrings are crafted in silver with intricate designs, featuring a large teardrop-shaped red stone at the center, surrounded by smaller pearls. The peacock motif adds a touch of elegance, making them ideal for weddings and other traditional events.\"</p>', 'Exquisite silver earrings featuring red stones and pearls, perfect for traditional occasions.\"', 399.00, 225.00, 11, 1, 1, 0, 'active', '[\"products\\/30n5M6JgEokJF2GEpOcFCJfXR2pHNWrrsCOKUy9Q.jpg\"]', '{\"description\":\"(e.g., handmade), packaging, or warranty information can be included in JSON format.\"}', NULL, 10.00, NULL, NULL, 'Ruby for the ref stone', 4.00, NULL, 0, '2025-07-19 02:48:41', '2025-09-04 01:18:03');

-- --------------------------------------------------------

--
-- Table structure for table `promocodes`
--

CREATE TABLE `promocodes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `type` enum('percentage','fixed') NOT NULL,
  `value` decimal(10,2) NOT NULL,
  `minimum_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `maximum_discount` decimal(10,2) DEFAULT NULL,
  `usage_limit` int(11) DEFAULT NULL,
  `usage_limit_per_user` int(11) NOT NULL DEFAULT 1,
  `used_count` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `starts_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `applicable_categories` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`applicable_categories`)),
  `applicable_products` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`applicable_products`)),
  `first_order_only` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `promocodes`
--

INSERT INTO `promocodes` (`id`, `code`, `name`, `description`, `type`, `value`, `minimum_amount`, `maximum_discount`, `usage_limit`, `usage_limit_per_user`, `used_count`, `is_active`, `starts_at`, `expires_at`, `applicable_categories`, `applicable_products`, `first_order_only`, `created_at`, `updated_at`) VALUES
(1, 'WELCOME10', 'Welcome Discount', '10% off for new customers', 'percentage', 10.00, 1000.00, 500.00, 100, 1, 0, 1, '2025-09-04 07:11:06', '2025-12-04 07:11:06', NULL, NULL, 1, '2025-09-04 01:41:06', '2025-09-04 01:41:06'),
(2, 'SAVE500', 'Fixed Discount', 'Flat ₹500 off on orders above ₹5000', 'fixed', 500.00, 5000.00, NULL, 50, 2, 0, 1, '2025-09-04 07:11:06', '2025-11-04 07:11:06', NULL, NULL, 0, '2025-09-04 01:41:06', '2025-09-04 01:41:06'),
(3, 'FESTIVE20', 'Festival Special', '20% off on all jewelry', 'percentage', 20.00, 2000.00, 2000.00, 200, 1, 0, 1, '2025-09-04 07:11:06', '2025-09-18 07:11:06', NULL, NULL, 0, '2025-09-04 01:41:06', '2025-09-04 01:41:06'),
(4, 'GOLD15', 'Gold Collection Discount', '15% off on gold jewelry', 'percentage', 15.00, 3000.00, 1500.00, NULL, 3, 0, 1, '2025-09-04 07:11:06', '2025-10-04 07:11:06', NULL, NULL, 0, '2025-09-04 01:41:06', '2025-09-04 01:41:06'),
(5, 'EXPIRED', 'Expired Code', 'This code has expired (for testing)', 'percentage', 25.00, 1000.00, 1000.00, 10, 1, 0, 1, '2025-08-21 07:11:06', '2025-08-28 07:11:06', NULL, NULL, 0, '2025-09-04 01:41:06', '2025-09-04 01:41:06');

-- --------------------------------------------------------

--
-- Table structure for table `promocode_usages`
--

CREATE TABLE `promocode_usages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `promocode_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `order_id` bigint(20) UNSIGNED DEFAULT NULL,
  `discount_amount` decimal(10,2) NOT NULL,
  `order_total` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reviews`
--

CREATE TABLE `reviews` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `product_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `order_item_id` bigint(20) UNSIGNED DEFAULT NULL,
  `rating` int(10) UNSIGNED NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `comment` text NOT NULL,
  `is_verified_purchase` tinyint(1) NOT NULL DEFAULT 0,
  `is_approved` tinyint(1) NOT NULL DEFAULT 1,
  `helpful_votes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`helpful_votes`)),
  `helpful_count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `key` varchar(255) NOT NULL,
  `value` text DEFAULT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'string',
  `group` varchar(255) NOT NULL DEFAULT 'general',
  `label` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `created_at`, `updated_at`) VALUES
(1, 'shipping_charge', '500', 'number', 'shipping', 'Standard Shipping Charge', 'Standard shipping charge in rupees', '2025-09-07 09:03:18', '2025-09-07 09:03:18'),
(2, 'free_shipping_threshold', '25000', 'number', 'shipping', 'Free Shipping Threshold', 'Minimum order amount for free shipping in rupees', '2025-09-07 09:03:18', '2025-09-07 09:03:18'),
(3, 'express_shipping_charge', '1000', 'number', 'shipping', 'Express Shipping Charge', 'Express shipping charge in rupees', '2025-09-07 09:03:18', '2025-09-07 09:03:18'),
(4, 'cod_charge', '50', 'number', 'shipping', 'Cash on Delivery Charge', 'Additional charge for cash on delivery in rupees', '2025-09-07 09:03:18', '2025-09-07 09:03:18'),
(5, 'gst_rate', '0.03', 'number', 'tax', 'GST Rate', 'GST rate as decimal (0.03 = 3%)', '2025-09-07 09:03:18', '2025-09-07 09:03:18'),
(6, 'site_name', 'Shreeji Marg', 'string', 'general', 'Site Name', 'Name of the website', '2025-09-07 09:03:18', '2025-09-07 09:03:18'),
(7, 'contact_email', '<EMAIL>', 'string', 'general', 'Contact Email', 'Primary contact email address', '2025-09-07 09:03:18', '2025-09-07 09:03:18'),
(8, 'contact_phone', '+91-9876543210', 'string', 'general', 'Contact Phone', 'Primary contact phone number', '2025-09-07 09:03:18', '2025-09-07 09:03:18');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(15) NOT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other','prefer-not-to-say') DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `timezone` varchar(255) DEFAULT NULL,
  `language` varchar(10) DEFAULT NULL,
  `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
  `role` enum('customer','admin','manager') NOT NULL DEFAULT 'customer',
  `preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`preferences`)),
  `login_count` int(11) NOT NULL DEFAULT 0,
  `total_spent` decimal(10,2) NOT NULL DEFAULT 0.00,
  `reward_points` int(11) NOT NULL DEFAULT 0,
  `membership_level` enum('regular','silver','gold','vip') NOT NULL DEFAULT 'regular',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `phone_verified_at` timestamp NULL DEFAULT NULL,
  `is_mobile_verified` tinyint(1) NOT NULL DEFAULT 0,
  `password` varchar(255) DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `last_login_ip` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `phone`, `date_of_birth`, `gender`, `avatar`, `bio`, `timezone`, `language`, `status`, `role`, `preferences`, `login_count`, `total_spent`, `reward_points`, `membership_level`, `last_login_at`, `email_verified_at`, `phone_verified_at`, `is_mobile_verified`, `password`, `remember_token`, `last_login_ip`, `created_at`, `updated_at`) VALUES
(1, 'Test User', '<EMAIL>', '9999999999', NULL, NULL, NULL, NULL, NULL, NULL, 'active', 'customer', NULL, 0, 0.00, 0, 'regular', NULL, NULL, NULL, 0, '$2y$12$XqlH/H8EGMRwKgH9cN76fuID605HIuhOIa.3BH/1Hw1U3PwKfheVq', NULL, NULL, '2025-08-23 11:31:05', '2025-08-23 11:31:05'),
(2, 'Prashant Gavel', '<EMAIL>', '**********', NULL, NULL, NULL, NULL, NULL, NULL, 'active', 'customer', NULL, 0, 10238.80, 100, 'regular', NULL, NULL, '2025-08-23 11:36:27', 0, NULL, NULL, NULL, '2025-08-23 11:36:27', '2025-09-08 13:36:09'),
(3, 'Akanksha', '<EMAIL>', '**********', NULL, NULL, NULL, NULL, NULL, NULL, 'active', 'admin', NULL, 0, 4198.37, 40, 'regular', '2025-09-08 13:36:31', NULL, '2025-08-24 08:13:54', 0, '$2y$12$RZhsR/dBQ2e/aNxl8e75ZO9u4vvuyPVhyvg9HqAJKr8x2K4aljhZy', NULL, '2409:40c4:23:84d8:91e:2ec7:47ea:29bd', '2025-08-24 08:13:54', '2025-09-08 13:36:31');

-- --------------------------------------------------------

--
-- Table structure for table `wishlists`
--

CREATE TABLE `wishlists` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `product_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `cart_items`
--
ALTER TABLE `cart_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `cart_items_product_id_foreign` (`product_id`),
  ADD KEY `cart_items_user_id_product_id_index` (`user_id`,`product_id`),
  ADD KEY `cart_items_session_id_product_id_index` (`session_id`,`product_id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `categories_slug_unique` (`slug`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `orders_order_number_unique` (`order_number`),
  ADD KEY `orders_user_id_foreign` (`user_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_items_order_id_foreign` (`order_id`),
  ADD KEY `order_items_product_id_foreign` (`product_id`);

--
-- Indexes for table `otp_verifications`
--
ALTER TABLE `otp_verifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `otp_verifications_phone_purpose_is_verified_index` (`phone`,`purpose`,`is_verified`),
  ADD KEY `otp_verifications_expires_at_index` (`expires_at`),
  ADD KEY `otp_verifications_session_id_index` (`session_id`),
  ADD KEY `otp_verifications_phone_index` (`phone`);

--
-- Indexes for table `pages`
--
ALTER TABLE `pages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `pages_slug_unique` (`slug`),
  ADD KEY `pages_created_by_foreign` (`created_by`),
  ADD KEY `pages_updated_by_foreign` (`updated_by`),
  ADD KEY `pages_slug_is_published_index` (`slug`,`is_published`),
  ADD KEY `pages_is_published_published_at_index` (`is_published`,`published_at`),
  ADD KEY `pages_show_in_menu_menu_order_index` (`show_in_menu`,`menu_order`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `products_slug_unique` (`slug`),
  ADD UNIQUE KEY `products_sku_unique` (`sku`),
  ADD KEY `products_category_id_foreign` (`category_id`);

--
-- Indexes for table `promocodes`
--
ALTER TABLE `promocodes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `promocodes_code_unique` (`code`);

--
-- Indexes for table `promocode_usages`
--
ALTER TABLE `promocode_usages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `promocode_usages_promocode_id_foreign` (`promocode_id`),
  ADD KEY `promocode_usages_user_id_foreign` (`user_id`),
  ADD KEY `promocode_usages_order_id_foreign` (`order_id`);

--
-- Indexes for table `reviews`
--
ALTER TABLE `reviews`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reviews_product_id_user_id_order_item_id_unique` (`product_id`,`user_id`,`order_item_id`),
  ADD KEY `reviews_order_item_id_foreign` (`order_item_id`),
  ADD KEY `reviews_product_id_is_approved_index` (`product_id`,`is_approved`),
  ADD KEY `reviews_user_id_index` (`user_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `settings_key_unique` (`key`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_phone_unique` (`phone`);

--
-- Indexes for table `wishlists`
--
ALTER TABLE `wishlists`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `wishlists_user_id_product_id_unique` (`user_id`,`product_id`),
  ADD KEY `wishlists_product_id_foreign` (`product_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `cart_items`
--
ALTER TABLE `cart_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=45;

--
-- AUTO_INCREMENT for table `otp_verifications`
--
ALTER TABLE `otp_verifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `pages`
--
ALTER TABLE `pages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `promocodes`
--
ALTER TABLE `promocodes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `promocode_usages`
--
ALTER TABLE `promocode_usages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `reviews`
--
ALTER TABLE `reviews`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `wishlists`
--
ALTER TABLE `wishlists`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `cart_items`
--
ALTER TABLE `cart_items`
  ADD CONSTRAINT `cart_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `cart_items_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pages`
--
ALTER TABLE `pages`
  ADD CONSTRAINT `pages_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `pages_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `promocode_usages`
--
ALTER TABLE `promocode_usages`
  ADD CONSTRAINT `promocode_usages_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `promocode_usages_promocode_id_foreign` FOREIGN KEY (`promocode_id`) REFERENCES `promocodes` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `promocode_usages_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `reviews`
--
ALTER TABLE `reviews`
  ADD CONSTRAINT `reviews_order_item_id_foreign` FOREIGN KEY (`order_item_id`) REFERENCES `order_items` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `reviews_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reviews_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `wishlists`
--
ALTER TABLE `wishlists`
  ADD CONSTRAINT `wishlists_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wishlists_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
