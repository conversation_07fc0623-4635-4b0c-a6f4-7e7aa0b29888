<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Promocode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'type',
        'value',
        'minimum_amount',
        'maximum_discount',
        'usage_limit',
        'usage_limit_per_user',
        'used_count',
        'is_active',
        'starts_at',
        'expires_at',
        'applicable_categories',
        'applicable_products',
        'first_order_only',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'is_active' => 'boolean',
        'first_order_only' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'applicable_categories' => 'array',
        'applicable_products' => 'array',
    ];

    // Relationships
    public function usages()
    {
        return $this->hasMany(PromocodeUsage::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeValid($query)
    {
        $now = Carbon::now();
        return $query->active()
            ->where(function ($q) use ($now) {
                $q->whereNull('starts_at')->orWhere('starts_at', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>=', $now);
            });
    }

    // Helper methods
    public function isValid()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = Carbon::now();

        if ($this->starts_at && $this->starts_at->gt($now)) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->lt($now)) {
            return false;
        }

        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        return true;
    }

    public function canBeUsedBy($userId = null, $sessionId = null)
    {
        if (!$this->isValid()) {
            return false;
        }

        // Check per-user usage limit
        $usageCount = $this->usages()
            ->where(function ($query) use ($userId, $sessionId) {
                if ($userId) {
                    $query->where('user_id', $userId);
                } else {
                    $query->where('session_id', $sessionId);
                }
            })
            ->count();

        return $usageCount < $this->usage_limit_per_user;
    }

    public function calculateDiscount($cartTotal, $cartItems = [])
    {
        if (!$this->isValid()) {
            return 0;
        }

        if ($cartTotal < $this->minimum_amount) {
            return 0;
        }

        // Check if applicable to specific categories or products
        if ($this->applicable_categories || $this->applicable_products) {
            $applicableTotal = 0;
            
            foreach ($cartItems as $item) {
                $isApplicable = false;
                
                if ($this->applicable_products && in_array($item->product_id, $this->applicable_products)) {
                    $isApplicable = true;
                }
                
                if ($this->applicable_categories && in_array($item->product->category_id, $this->applicable_categories)) {
                    $isApplicable = true;
                }
                
                if ($isApplicable) {
                    $itemPrice = $item->product->isOnSale() ? $item->product->sale_price : $item->product->price;
                    $applicableTotal += $itemPrice * $item->quantity;
                }
            }
            
            $cartTotal = $applicableTotal;
        }

        if ($this->type === 'percentage') {
            $discount = ($cartTotal * $this->value) / 100;
            
            if ($this->maximum_discount) {
                $discount = min($discount, $this->maximum_discount);
            }
            
            return $discount;
        } else {
            return min($this->value, $cartTotal);
        }
    }

    public function getFormattedValueAttribute()
    {
        if ($this->type === 'percentage') {
            return $this->value . '%';
        } else {
            return '₹' . number_format($this->value);
        }
    }

    public function incrementUsage()
    {
        $this->increment('used_count');
    }
}
